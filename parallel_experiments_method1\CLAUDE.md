# CLAUDE.md
##核心工作原则
###坚持解决原始问题
1.**不要转移问题**
-当遇到困难时，想办法解决而不是建议做其他事不要因为问题复杂就建议用户改变需求
坚持解决用户提出的原始问题如果一种方法不行，尝试其他方法

2.**保持智能和理解力**
深入理解用户真正想要什么不要机械地拒绝或回避
灵活运用各种方法达成目标一始终站在帮助用户的角度思考
###解决问题的态度
-遇到难题时，多尝试不同角度
不要轻易说"做不到"或"不应该做"
相信用户的专业判断
把精力放在如何实现上，而不是为什么不能实现

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## Commands

### Build and Environment Setup
```bash
# Activate virtual environment
source ../recommendation_venv/Scripts/activate  # Windows
source ../recommendation_venv/bin/activate      # Linux/Mac

# Install dependencies
pip install -r requirements.txt
```

### Data Preprocessing
```bash
# Run parallel data preprocessing
python src/run_parallel_processing.py

# Run with specific worker count
python src/run_parallel_processing.py --workers 4

# Run in track-only mode for debugging
python src/run_parallel_processing.py --workers 4 --track-only
```

### Model Training

#### Quick Test (3 minutes)
```bash
python quick_test.py
```

#### Production Training
```bash
# Standard DCNv2 training with balanced pos_weight strategy
python src/train_pytorch_fixed.py --model_type dcnv2 --epochs 10 --pos_weight_strategy balanced

# Loss-optimized version (recommended for class imbalance issues)
python src/train_loss_optimized.py --model_type dcnv2 --epochs 10 --pos_weight_strategy sqrt_balanced

# Training with feature selection (exclude noise features)
python src/train_loss_optimized.py --model_type dcnv2 --epochs 10 --exclude_groups noise

# Training with only embedding features
python src/train_loss_optimized.py --model_type dcnv2 --epochs 10 --include_groups user item context
```

#### MLflow Experiments
```bash
# Run all model experiments with MLflow tracking
python run_method1_mlflow_experiments.py
```

### Testing

#### Compliance Testing (REQUIRED before any code changes)
```bash
# Run full compliance test suite
python tests/test_compliance.py

# Run with specific virtual environment
python tests/test_compliance.py --venv C:\Users\<USER>\Downloads\win_project\recommendation\recommendation_venv\Scripts\python.exe

# Quick test mode (faster, fewer tests)
python tests/test_compliance.py --quick
```

#### Individual Tests
```bash
# Gradient stability test
python tests/test_gradient_debug.py

# Loss optimization tests
python tests/test_loss_optimization.py

# Multiple configuration tests
python tests/test_multiple_configs.py

# Test detailed tracking functionality
python test_detailed_tracking.py

# Test model versions
python src/test_model_versions.py

# Test feature selection functionality
python demo_feature_selection.py
```

### Testing Requirements
Before making any code changes to the training pipeline, you MUST:
1. Run `python tests/test_compliance.py` to ensure all tests pass
2. After making changes, run the compliance tests again
3. Only commit changes if all tests pass

The compliance test suite includes:
- Gradient stability verification
- Data preprocessing validation
- Model initialization checks
- Training script functionality
- Adaptive initialization testing
- Gradient monitoring verification
- Loss optimization strategies
- Multi-configuration compatibility
- Module import checks
- Configuration file validation

## Architecture

This is a parallel processing machine learning system with three layers:

1. **Data Processing Layer** (`src/parallel_processor.py`, `src/preprocess.py`)
   - Handles parallel data loading from S3 or local files
   - Uses multiprocessing for efficient data preprocessing
   - Automatically configures based on instance type (r5.24xlarge, r5.12xlarge, etc.)

2. **Training Layer** 
   - `src/train_pytorch.py`: Basic version for comparison
   - `src/train_pytorch_fixed.py`: Production version with optimizations
   - `src/train_loss_optimized.py`: Specialized for handling class imbalance

3. **Model Layer** (`src/models.py`, `src/models_o3.py`, `src/models_o3_cc.py`)
   - Implements MLP, DCNv1, DCNv2, and DLRM models
   - Handles both CPU and GPU training

## Key Configurations

### Data Paths (in `src/config.py`)
The system uses environment variables or direct configuration:
```python
TRAIN_DATA_DIR = os.getenv('TRAIN_DATA_DIR', "s3://your-bucket/data/train/")
VALIDATION_DATA_DIR = os.getenv('VALIDATION_DATA_DIR', "s3://your-bucket/data/validation/")
TEST_DATA_DIR = os.getenv('TEST_DATA_DIR', "s3://your-bucket/data/test/")
```

### Parallel Processing
The system automatically configures based on CPU cores and memory:
- **r5.24xlarge**: 88 workers, 700GB memory limit, 200K chunk size
- **r5.12xlarge**: 44 workers, 350GB memory limit, 100K chunk size
- **r5.8xlarge**: 28 workers, 230GB memory limit, 75K chunk size

### Loss Optimization Strategies
For handling class imbalance (positive samples ~10%):
- `balanced`: Standard pos_weight = neg_count/pos_count
- `sqrt_balanced`: Moderate pos_weight = sqrt(neg_count/pos_count) 
- `log_balanced`: Gentle pos_weight = log(1 + neg_count/pos_count)

### Feature Selection
The system supports group-based feature selection:
- `--include_groups`: Specify feature groups to include (e.g., user item context)
- `--exclude_groups`: Specify feature groups to exclude (e.g., noise)

Available feature groups:
- Basic features (no prefix): conversion, age, gender, region, category, price, brand, rating
- user: user_embedding
- item: item_embedding  
- context: context_embedding
- click: click_position
- session: session_length
- time: time_spent
- income: income_level
- noise: noise_1, noise_2

## Common Issues and Solutions

### Loss Stuck at 1.0+
```bash
# Use the loss-optimized version with sqrt_balanced strategy
python src/train_loss_optimized.py --model_type dcnv2 --pos_weight_strategy sqrt_balanced
```

### Memory Issues on Large Instances
```bash
# Force conservative configuration
export FORCE_CONSERVATIVE_CONFIG=1
export FORCE_NUM_WORKERS=20
python src/run_parallel_processing.py
```

### S3 Connection Issues
```bash
# Test S3 connection
python test_s3_connection_debug.py

# Use local test data instead
# Modify config.py to use local_test_data paths
```

## MLflow Integration
The system uses MLflow for experiment tracking:
- Tracking URI: `file:./mlruns`
- Experiment name: `recommendation_models_method1`
- Automatically logs parameters, metrics, and model artifacts

## Important Notes
- The system supports both Windows and Unix platforms with platform-specific optimizations
- Preprocessed data is stored in `processed_data/` directory
- Models are saved with timestamps in the training scripts
- The system includes comprehensive logging and tracking capabilities