"""
Generate correct feature metadata for expanded arrays
"""
import os
import json
import pandas as pd
import numpy as np

def generate_expanded_feature_metadata():
    """
    Generate feature metadata that accounts for array expansion
    """
    
    # Read sample parquet to understand structure
    sample_file = r"local_test_data\small\train\data_chunk_000.parquet"
    df = pd.read_parquet(sample_file)
    
    # Load analysis results
    with open('data_analysis/data_analysis_results.json', 'r') as f:
        analysis_results = json.load(f)
    
    # Define excluded columns and label
    label_column = "click"
    excluded_columns = ["user_id", "item_id", "timestamp"]
    
    feature_metadata = {
        'features': [],
        'groups': {},
        'total_features': 0,
        'label_column': label_column,
        'excluded_columns': excluded_columns,
        'original_columns': list(df.columns)
    }
    
    feature_index = 0
    
    # Process numeric columns first
    for col in analysis_results['summary']['numeric_columns']:
        if col == label_column or col in excluded_columns:
            continue
            
        prefix = col.split('_')[0] if '_' in col else ''
        
        if prefix not in feature_metadata['groups']:
            feature_metadata['groups'][prefix] = []
        
        feature_info = {
            'index': feature_index,
            'name': col,
            'type': 'numeric',
            'group': prefix,
            'original_column': col
        }
        
        feature_metadata['features'].append(feature_info)
        feature_metadata['groups'][prefix].append(feature_index)
        feature_index += 1
    
    # Process categorical columns
    for col in analysis_results['summary']['categorical_columns']:
        if col == label_column or col in excluded_columns:
            continue
            
        prefix = col.split('_')[0] if '_' in col else ''
        
        if prefix not in feature_metadata['groups']:
            feature_metadata['groups'][prefix] = []
        
        feature_info = {
            'index': feature_index,
            'name': col,
            'type': 'categorical',
            'group': prefix,
            'original_column': col
        }
        
        feature_metadata['features'].append(feature_info)
        feature_metadata['groups'][prefix].append(feature_index)
        feature_index += 1
    
    # Process array columns (expanded)
    for col in analysis_results['summary']['array_columns']:
        if col == label_column or col in excluded_columns:
            continue
            
        prefix = col.split('_')[0] if '_' in col else ''
        
        if prefix not in feature_metadata['groups']:
            feature_metadata['groups'][prefix] = []
        
        # Get array dimension
        array_dim = analysis_results['column_analysis'][col]['array_length']
        
        # Add each expanded feature
        for i in range(array_dim):
            feature_info = {
                'index': feature_index,
                'name': f"{col}_{i}",
                'type': 'embedding_element',
                'group': prefix,
                'original_column': col,
                'embedding_index': i
            }
            
            feature_metadata['features'].append(feature_info)
            feature_metadata['groups'][prefix].append(feature_index)
            feature_index += 1
    
    feature_metadata['total_features'] = feature_index
    
    # Save metadata
    os.makedirs('processed_data', exist_ok=True)
    metadata_file = 'processed_data/feature_metadata_expanded.json'
    
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(feature_metadata, f, indent=2, ensure_ascii=False)
    
    print(f"\nExpanded feature metadata saved to: {metadata_file}")
    print(f"Total features: {feature_metadata['total_features']}")
    print(f"\nFeature groups:")
    
    for group, indices in sorted(feature_metadata['groups'].items()):
        group_name = group if group else "(no prefix)"
        print(f"  {group_name}: {len(indices)} features")
    
    # Summary of features by type
    print(f"\nFeature breakdown:")
    print(f"  Numeric features: {len(analysis_results['summary']['numeric_columns'])}")
    print(f"  Categorical features: {len(analysis_results['summary']['categorical_columns'])}")
    print(f"  Array features expanded:")
    for col in analysis_results['summary']['array_columns']:
        dim = analysis_results['column_analysis'][col]['array_length']
        print(f"    {col}: {dim} dimensions")
    
    return feature_metadata

if __name__ == "__main__":
    generate_expanded_feature_metadata()