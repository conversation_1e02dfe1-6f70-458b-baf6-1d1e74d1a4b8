# 数据预处理与特征工程深度分析：数组特征处理的艺术

> **注意**: 本文档由Claude (Opus 4)模型创建和编写，基于项目代码的深入分析。

## 🎯 概述

本文档深入分析推荐系统项目中的数据预处理策略，特别聚焦于数组特征（Array Features）的处理技术。这是推荐系统中处理embedding、序列数据等复杂特征的关键技术。

### 📖 核心术语解释

**缩写说明**：
- **ML** (Machine Learning): 机器学习
- **NLP** (Natural Language Processing): 自然语言处理
- **CV** (Computer Vision): 计算机视觉
- **CTR** (Click-Through Rate): 点击率
- **CVR** (Conversion Rate): 转化率
- **2D/3D**: 二维/三维数组
- **L2**: L2范数，欧几里得距离
- **GC** (Garbage Collection): 垃圾回收
- **MB/GB**: Megabyte/Gigabyte（兆字节/千兆字节）

**技术术语**：
- **Embedding**: 嵌入向量，将离散对象映射到连续向量空间
- **Array Features**: 数组特征，存储为列表或数组形式的特征
- **Padding**: 填充，将序列补充到固定长度
- **Truncation**: 截断，将超长序列裁剪到固定长度
- **Normalization**: 归一化，将数据缩放到特定范围
- **Sparse Matrix**: 稀疏矩阵，大部分元素为零的矩阵
- **Feature Hashing**: 特征哈希，将高维特征映射到低维空间
- **Multi-hot Encoding**: 多热编码，多个类别同时为1的编码方式

**变量命名解释**：
- `array_col/array_column`: 数组列，包含数组数据的DataFrame列
- `expected_length/target_length`: 期望/目标长度，数组应该具有的统一长度
- `embedding_dim`: 嵌入维度，embedding向量的长度
- `vocab_size`: 词汇表大小，用于序列数据的最大ID值
- `batch_size`: 批次大小，一次处理的样本数
- `col_names`: 列名列表，展开后的特征名称
- `dtype`: 数据类型（如float32, int64等）

## 📊 特征类型识别与分类

### 项目中的特征分类体系

根据PROJECT_DESIGN_DOCUMENT.md和代码分析，项目将特征分为三大类：

1. **数值特征（Numeric）**：连续值特征
   - 例如：age（年龄）、price（价格）、rating（评分）
   - 特点：可直接进行数学运算
   
2. **类别特征（Categorical）**：离散类别
   - 例如：gender（性别）、region（地区）、category（类别）
   - 特点：需要编码后才能用于模型
   
3. **数组特征（Array）**：向量或序列数据
   - 例如：user_embedding（用户嵌入）、item_embedding（物品嵌入）
   - 特点：每个样本包含多个数值的列表

### 自动特征类型检测

```python
# 📍 基于项目代码的分析（由Claude整理）
class DataAnalyzer:
    def analyze_parquet_structure(self, file_path):
        # nrows=1000: 只读取前1000行作为样本，节省内存
        sample_df = pd.read_parquet(file_path, nrows=1000)
        
        for col in sample_df.columns:
            col_data = sample_df[col]
            
            # 检测数组列的核心逻辑
            if self._is_array_column(col_data):
                # 这是项目的创新点：自动识别数组特征
                # iloc[0]: 获取第一行数据来判断数组长度
                array_length = len(col_data.iloc[0])
                column_info[col] = {
                    'type': 'array',
                    'length': array_length,  # 数组的固定长度
                    'dtype': str(col_data.iloc[0].dtype)  # 数组元素的数据类型
                }
```

### 数组特征的典型场景

```python
# 📍 由Claude基于推荐系统经验补充的典型数组特征
典型数组特征示例：
1. user_embedding: [0.23, -0.15, 0.87, ...]  # 用户向量表示（通常128-512维）
2. item_embedding: [0.45, 0.12, -0.33, ...]  # 物品向量表示（维度同user）  
3. click_sequence: [101, 245, 789, ...]      # 点击序列（最近N次点击的物品ID）
4. category_multi_hot: [0, 1, 0, 1, 0, ...]  # 多标签编码（物品属于多个类别）
5. time_series_features: [0.8, 0.7, 0.9, ...] # 时序特征（如最近N天的活跃度）
```

## 🔧 数组特征处理的核心技术

### 1. 数组展开（Array Expansion）

项目采用了将数组特征展开为多个标量特征的策略：

```python
# 📍 项目中的实现逻辑（由Claude分析提取）
def _expand_array_column(self, array_col, expected_length):
    """将数组列展开为多个特征"""
    try:
        # 确保所有数组长度一致
        arrays = []
        for item in array_col:
            if isinstance(item, (list, np.ndarray)):
                arr = np.array(item)
                if len(arr) == expected_length:
                    arrays.append(arr)
                else:
                    # 长度不匹配，填充或截断
                    if len(arr) < expected_length:
                        # 填充零
                        padded = np.zeros(expected_length)
                        padded[:len(arr)] = arr
                        arrays.append(padded)
                    else:
                        # 截断
                        arrays.append(arr[:expected_length])
        
        # 转换为2D数组
        return np.vstack(arrays)
    except Exception as e:
        logger.error(f"数组展开失败: {e}")
        return None
```

### 2. 智能填充策略

```python
# 📍 由Claude补充的高级填充策略
class ArrayFeaturePadding:
    """数组特征的智能填充策略"""
    
    @staticmethod
    def pad_sequence(sequence, target_length, strategy='zero'):
        """
        填充序列到目标长度
        
        策略：
        - 'zero': 零填充
        - 'mean': 均值填充
        - 'last': 重复最后一个值
        - 'interpolate': 插值填充
        """
        current_length = len(sequence)
        
        if current_length >= target_length:
            return sequence[:target_length]
        
        if strategy == 'zero':
            return np.pad(sequence, (0, target_length - current_length), 'constant')
        
        elif strategy == 'mean':
            mean_val = np.mean(sequence)
            return np.pad(sequence, (0, target_length - current_length), 
                         'constant', constant_values=mean_val)
        
        elif strategy == 'last':
            return np.pad(sequence, (0, target_length - current_length), 'edge')
        
        elif strategy == 'interpolate':
            # 线性插值到目标长度
            x_old = np.linspace(0, 1, current_length)
            x_new = np.linspace(0, 1, target_length)
            return np.interp(x_new, x_old, sequence)
```

### 3. 数组特征的归一化

```python
# 📍 由Claude分析项目需求后补充
def normalize_array_features(array_features, norm_type='standard'):
    """
    数组特征的归一化处理
    
    特殊考虑：
    1. 保持数组内部的相对关系
    2. 处理不同尺度的数组元素
    """
    if norm_type == 'standard':
        # 标准化：每个位置独立标准化
        mean = np.mean(array_features, axis=0)
        std = np.std(array_features, axis=0) + 1e-8
        return (array_features - mean) / std
    
    elif norm_type == 'minmax':
        # 最小-最大归一化
        min_val = np.min(array_features, axis=0)
        max_val = np.max(array_features, axis=0)
        return (array_features - min_val) / (max_val - min_val + 1e-8)
    
    elif norm_type == 'l2':
        # L2归一化：适用于embedding
        norms = np.linalg.norm(array_features, axis=1, keepdims=True)
        return array_features / (norms + 1e-8)
```

## 📈 实际案例分析

### 案例1：用户Embedding处理

```python
# 📍 由Claude基于项目场景构建的案例
# 原始数据示例
user_data = pd.DataFrame({
    'user_id': [1, 2, 3],
    'user_embedding': [
        [0.23, -0.15, 0.87, 0.45],  # 4维embedding
        [0.12, 0.34, -0.56],         # 3维（缺失）
        None                         # 完全缺失
    ]
})

# 处理流程
def process_user_embeddings(df, embedding_dim=4):
    """处理用户embedding特征"""
    
    embeddings = []
    for idx, row in df.iterrows():
        emb = row['user_embedding']
        
        if emb is None:
            # 策略1：使用零向量
            processed_emb = np.zeros(embedding_dim)
            
        elif len(emb) < embedding_dim:
            # 策略2：填充到目标维度
            processed_emb = np.pad(emb, (0, embedding_dim - len(emb)), 'constant')
            
        else:
            processed_emb = np.array(emb[:embedding_dim])
        
        embeddings.append(processed_emb)
    
    # 转换为特征矩阵
    feature_matrix = np.vstack(embeddings)
    
    # 创建列名
    col_names = [f'user_emb_{i}' for i in range(embedding_dim)]
    
    return pd.DataFrame(feature_matrix, columns=col_names)
```

### 案例2：序列特征处理

```python
# 📍 由Claude补充的序列特征处理案例
def process_click_sequence(sequences, max_length=50, vocab_size=10000):
    """
    处理点击序列特征
    
    挑战：
    1. 序列长度不一
    2. 需要保留时序信息
    3. 稀疏性处理
    """
    
    processed_sequences = []
    
    for seq in sequences:
        if seq is None or len(seq) == 0:
            # 空序列处理
            processed = np.zeros(max_length, dtype=np.int32)
        else:
            # 转换为numpy数组
            seq_array = np.array(seq, dtype=np.int32)
            
            # 过滤无效ID
            seq_array = seq_array[seq_array < vocab_size]
            
            if len(seq_array) > max_length:
                # 保留最近的点击
                processed = seq_array[-max_length:]
            else:
                # 左填充（保持时序）
                processed = np.pad(seq_array, 
                                 (max_length - len(seq_array), 0), 
                                 'constant')
        
        processed_sequences.append(processed)
    
    return np.vstack(processed_sequences)
```

## 🎯 性能优化技巧

### 1. 批量处理优化

```python
# 📍 由Claude基于项目需求设计的优化方案
class BatchArrayProcessor:
    """批量处理数组特征，提高效率"""
    
    def __init__(self, batch_size=10000):
        self.batch_size = batch_size
        
    def process_in_batches(self, array_column, process_func):
        """分批处理大规模数组数据"""
        n_samples = len(array_column)
        results = []
        
        for start_idx in range(0, n_samples, self.batch_size):
            end_idx = min(start_idx + self.batch_size, n_samples)
            batch = array_column[start_idx:end_idx]
            
            # 处理批次
            batch_result = process_func(batch)
            results.append(batch_result)
            
            # 内存清理
            if start_idx % (self.batch_size * 10) == 0:
                gc.collect()
        
        return np.vstack(results)
```

### 2. 内存效率优化

```python
# 📍 由Claude设计的内存优化策略
def optimize_array_dtype(array_features):
    """优化数组特征的数据类型以节省内存"""
    
    # 分析数值范围
    min_val = np.min(array_features)
    max_val = np.max(array_features)
    
    # 选择合适的数据类型
    if min_val >= 0 and max_val <= 255:
        return array_features.astype(np.uint8)
    elif min_val >= -128 and max_val <= 127:
        return array_features.astype(np.int8)
    elif min_val >= -32768 and max_val <= 32767:
        return array_features.astype(np.int16)
    elif np.all(np.isclose(array_features, array_features.astype(np.float32))):
        return array_features.astype(np.float32)
    else:
        return array_features  # 保持float64
```

## 🔍 常见问题与解决方案

### 问题1：数组长度不一致

**症状**：
```
ValueError: all input arrays must have the same shape
```

**解决方案**：
```python
# 📍 项目中采用的解决方案（由Claude总结）
def standardize_array_lengths(array_list):
    """标准化数组长度"""
    # 1. 统计长度分布
    lengths = [len(arr) for arr in array_list if arr is not None]
    
    # 2. 选择目标长度（如：95分位数）
    # 为什么用95分位数：覆盖大部分数据，同时避免极端值影响
    target_length = int(np.percentile(lengths, 95))
    
    # 3. 统一处理
    standardized = []
    for arr in array_list:
        if arr is None:
            # None值用零向量替代
            standardized.append(np.zeros(target_length))
        else:
            # pad_or_truncate: 自定义函数，填充或截断到目标长度
            standardized.append(pad_or_truncate(arr, target_length))
    
    return np.array(standardized)
```

### 问题2：稀疏数组的存储

**挑战**：大量零值导致内存浪费

**解决方案**：
```python
# 📍 由Claude建议的稀疏存储方案
from scipy.sparse import csr_matrix

def convert_to_sparse_if_beneficial(array_features, threshold=0.1):
    """当稀疏度高时转换为稀疏矩阵"""
    
    # 计算稀疏度
    sparsity = 1.0 - np.count_nonzero(array_features) / array_features.size
    
    if sparsity > threshold:
        # 转换为稀疏矩阵
        sparse_features = csr_matrix(array_features)
        
        # 计算内存节省
        dense_size = array_features.nbytes
        sparse_size = sparse_features.data.nbytes + \
                     sparse_features.indices.nbytes + \
                     sparse_features.indptr.nbytes
        
        savings = 1 - sparse_size / dense_size
        logger.info(f"转换为稀疏矩阵，节省内存: {savings:.1%}")
        
        return sparse_features
    
    return array_features
```

## 📊 特征工程最佳实践

### 1. 特征命名规范

```python
# 📍 由Claude总结的命名规范
def generate_array_feature_names(base_name, array_length):
    """生成数组特征的标准命名"""
    return [f"{base_name}_{i:03d}" for i in range(array_length)]

# 示例：
# user_embedding -> user_embedding_000, user_embedding_001, ...
# click_sequence -> click_sequence_000, click_sequence_001, ...
```

### 2. 特征元数据管理

```python
# 📍 项目中的元数据管理（由Claude增强）
class FeatureMetadata:
    """管理特征的元数据信息"""
    
    def __init__(self):
        self.metadata = {
            'array_features': {},
            'numeric_features': [],
            'categorical_features': []
        }
    
    def register_array_feature(self, original_name, expanded_names, 
                             array_length, dtype, processing_method):
        """注册数组特征的元数据"""
        self.metadata['array_features'][original_name] = {
            'expanded_names': expanded_names,
            'length': array_length,
            'dtype': str(dtype),
            'processing_method': processing_method,
            'created_at': datetime.now().isoformat()
        }
    
    def save_metadata(self, output_path):
        """保存元数据供后续使用"""
        with open(output_path, 'w') as f:
            json.dump(self.metadata, f, indent=2)
```

## 🚀 高级技巧

### 1. 动态数组长度选择

```python
# 📍 由Claude设计的动态长度选择算法
def determine_optimal_array_length(array_column, memory_budget_mb=100):
    """
    基于数据分布和内存预算动态选择数组长度
    """
    # 收集长度统计
    lengths = [len(arr) for arr in array_column if arr is not None]
    
    # 计算不同百分位数的长度
    percentiles = [50, 75, 90, 95, 99]
    length_options = [int(np.percentile(lengths, p)) for p in percentiles]
    
    # 估算内存使用
    n_samples = len(array_column)
    dtype_size = 4  # float32
    
    for length in length_options:
        memory_usage_mb = (n_samples * length * dtype_size) / (1024 * 1024)
        
        if memory_usage_mb <= memory_budget_mb:
            optimal_length = length
        else:
            break
    
    logger.info(f"选择最优数组长度: {optimal_length} "
                f"(覆盖{np.mean(lengths <= optimal_length)*100:.1f}%的样本)")
    
    return optimal_length
```

### 2. 特征哈希处理超高维数组

```python
# 📍 由Claude建议的特征哈希方案
from sklearn.feature_extraction import FeatureHasher

def hash_high_dim_arrays(array_features, target_dim=1000):
    """
    使用特征哈希处理超高维数组
    适用于：稀疏的高维categorical数组
    """
    hasher = FeatureHasher(n_features=target_dim, input_type='pair')
    
    hashed_features = []
    for arr in array_features:
        # 将数组转换为(index, value)对
        pairs = [(str(i), val) for i, val in enumerate(arr) if val != 0]
        
        # 哈希处理
        hashed = hasher.transform([pairs]).toarray().flatten()
        hashed_features.append(hashed)
    
    return np.vstack(hashed_features)
```

## 🎯 总结

数组特征处理是推荐系统中的核心技术挑战。项目通过以下策略实现了高效的数组特征处理：

1. **自动检测**：智能识别数组类型特征
2. **标准化处理**：统一数组长度，确保兼容性
3. **高效存储**：使用合适的数据类型和稀疏表示
4. **灵活扩展**：支持多种填充和归一化策略

这些技术确保了系统能够处理各种复杂的特征类型，为模型训练提供了高质量的输入数据。

---

*本文档由Claude (Opus 4)基于项目代码分析创建，深入解析了数组特征处理的技术细节和最佳实践。*

## 🔍 代码机制分析与改进建议

### 文档与实际代码的对比

经过分析 `preprocess.py` 中的实际实现，发现以下差异：

1. **数组特征检测**：
   - **文档描述**：使用 `_is_array_column` 进行自动检测
   - **实际代码**：在 `data_analyzer.py` 中通过检查第一个非空值是否为list/ndarray来判断
   - **实现细节**：
   ```python
   # 实际的检测逻辑
   first_valid_idx = col_data.first_valid_index()
   if first_valid_idx is not None:
       first_value = col_data.iloc[0]
       if isinstance(first_value, (list, np.ndarray)):
           # 是数组列
   ```

2. **数组展开实现**：
   - **文档描述**：展示了通用的展开策略
   - **实际代码**：`_expand_array_column` 方法确实存在，并实现了填充/截断逻辑
   - **优点**：实际实现考虑了多种边界情况（None值、长度不匹配等）

3. **特征元数据管理**：
   - **文档描述**：展示了理想的元数据管理方案
   - **实际代码**：实现了完整的 `feature_metadata` 系统，包括：
     - 特征索引管理
     - 特征分组（按前缀）
     - 展开后的特征命名（如 `user_embedding_0`, `user_embedding_1`）
   - **亮点**：`save_metadata` 方法会保存完整的特征映射信息

### 改进建议

#### 1. 增强数组长度的自适应选择

```python
def adaptive_array_length_selection(self, col_name: str, array_samples: List) -> int:
    """基于数据分布智能选择数组长度"""
    # 收集有效数组的长度
    lengths = [len(arr) for arr in array_samples 
               if isinstance(arr, (list, np.ndarray))]
    
    if not lengths:
        return 0
    
    # 计算统计信息
    length_counts = pd.Series(lengths).value_counts()
    most_common_length = length_counts.index[0]
    coverage = length_counts.iloc[0] / len(lengths)
    
    # 如果最常见长度覆盖超过80%的样本，使用它
    if coverage > 0.8:
        return most_common_length
    
    # 否则使用95分位数，确保覆盖大部分数据
    return int(np.percentile(lengths, 95))
```

#### 2. 优化内存使用的稀疏数组处理

```python
def process_sparse_array_column(self, series: pd.Series, expected_dim: int, 
                               sparsity_threshold: float = 0.9) -> Optional[np.ndarray]:
    """处理稀疏数组特征，节省内存"""
    # 先按常规方式展开
    expanded = self._expand_array_column(series, expected_dim)
    
    if expanded is not None:
        # 计算稀疏度
        sparsity = 1.0 - np.count_nonzero(expanded) / expanded.size
        
        if sparsity > sparsity_threshold:
            # 记录哪些位置经常为非零
            non_zero_freq = np.sum(expanded != 0, axis=0) / len(expanded)
            # 只保留频繁出现非零值的维度
            important_dims = np.where(non_zero_freq > 0.01)[0]
            
            if len(important_dims) < expected_dim * 0.5:
                logger.info(f"列 {series.name} 稀疏度高({sparsity:.1%})，"
                          f"压缩维度: {expected_dim} -> {len(important_dims)}")
                return expanded[:, important_dims]
    
    return expanded
```

#### 3. 添加数组特征的统计信息

```python
def analyze_array_feature_stats(self, array_column: pd.Series) -> Dict:
    """分析数组特征的统计信息，用于后续优化"""
    stats = {
        'name': array_column.name,
        'null_ratio': array_column.isnull().mean(),
        'length_distribution': {},
        'value_statistics': {},
        'sparsity': 0.0
    }
    
    valid_arrays = [arr for arr in array_column if isinstance(arr, (list, np.ndarray))]:
    
    if valid_arrays:
        # 长度分布
        lengths = [len(arr) for arr in valid_arrays]
        stats['length_distribution'] = {
            'min': min(lengths),
            'max': max(lengths),
            'mean': np.mean(lengths),
            'std': np.std(lengths),
            'p50': np.percentile(lengths, 50),
            'p95': np.percentile(lengths, 95)
        }
        
        # 数值统计（展开后）
        all_values = np.concatenate([np.array(arr).flatten() for arr in valid_arrays])
        stats['value_statistics'] = {
            'min': float(np.min(all_values)),
            'max': float(np.max(all_values)),
            'mean': float(np.mean(all_values)),
            'std': float(np.std(all_values))
        }
        
        # 稀疏度
        stats['sparsity'] = float(np.sum(all_values == 0) / len(all_values))
    
    return stats
```

#### 4. 批量处理优化

当前的实现是逐个样本处理数组，可以优化为向量化操作：

```python
def _expand_array_column_vectorized(self, series: pd.Series, expected_dim: int) -> Optional[np.ndarray]:
    """向量化的数组展开，提高性能"""
    # 预分配结果数组
    result = np.zeros((len(series), expected_dim), dtype=np.float32)
    
    # 批量处理
    for idx, item in enumerate(series):
        if isinstance(item, (list, np.ndarray)):
            arr = np.asarray(item, dtype=np.float32)
            # 使用切片操作，避免条件判断
            min_len = min(len(arr), expected_dim)
            result[idx, :min_len] = arr[:min_len]
        # 如果item是None或其他类型，保持为零（已经初始化）
    
    return result
```

### 性能优化建议

1. **并行化数组处理**：
   ```python
   from concurrent.futures import ThreadPoolExecutor
   
   def parallel_expand_arrays(self, array_columns: List[str], df: pd.DataFrame) -> List[np.ndarray]:
       """并行展开多个数组列"""
       with ThreadPoolExecutor(max_workers=4) as executor:
           futures = []
           for col in array_columns:
               dim = self.array_dimensions.get(col, 0)
               future = executor.submit(self._expand_array_column, df[col], dim)
               futures.append(future)
           
           return [f.result() for f in futures if f.result() is not None]
   ```

2. **缓存展开结果**：
   ```python
   from functools import lru_cache
   
   @lru_cache(maxsize=100)
   def get_array_padding_strategy(self, col_name: str, expected_dim: int) -> str:
       """缓存每列的填充策略，避免重复计算"""
       # 基于列名和维度返回最优策略
       if 'embedding' in col_name:
           return 'zero'  # Embedding用零填充
       elif 'sequence' in col_name:
           return 'last'  # 序列用最后值填充
       else:
           return 'mean'  # 其他用均值填充
   ```

### 总结

实际代码实现已经相当完善，主要优势：
1. 完整的数组特征检测和处理流程
2. 灵活的填充/截断策略
3. 详细的特征元数据记录

建议的改进方向：
1. 增加自适应的数组长度选择
2. 针对稀疏数组的内存优化
3. 向量化操作提升性能
4. 并行处理多个数组列

这些改进将使系统在处理大规模数组特征时更加高效和智能。