"""
Test preprocessing with array expansion
"""
import os
import sys
import numpy as np
import pandas as pd
import json

# Add src to path
sys.path.append('src')

from preprocess import IntelligentPreprocessor, preprocess_all_datasets

# First, create proper data analysis results
print("Creating data analysis results...")

# Read sample data to analyze
df = pd.read_parquet('local_test_data/small/train/data_chunk_000.parquet')

# Analyze columns
array_columns = []
numeric_columns = []
categorical_columns = []

for col in df.columns:
    if col in ["user_id", "item_id", "timestamp", "click"]:
        continue
        
    sample_value = df[col].iloc[0]
    if isinstance(sample_value, (list, np.ndarray)):
        array_columns.append(col)
    elif pd.api.types.is_numeric_dtype(df[col]):
        numeric_columns.append(col)
    else:
        categorical_columns.append(col)

# Create analysis results
analysis_results = {
    "summary": {
        "detected_label_column": "click",
        "array_columns": array_columns,
        "numeric_columns": numeric_columns,
        "categorical_columns": categorical_columns
    },
    "column_analysis": {}
}

# Add array dimensions
for col in array_columns:
    sample_value = df[col].iloc[0]
    analysis_results["column_analysis"][col] = {
        "array_length": len(sample_value)
    }

# Save analysis results
os.makedirs('data_analysis', exist_ok=True)
with open('data_analysis/data_analysis_results.json', 'w') as f:
    json.dump(analysis_results, f, indent=2)

print(f"Analysis results saved. Found:")
print(f"  Array columns: {array_columns}")
print(f"  Numeric columns: {len(numeric_columns)}")
print(f"  Categorical columns: {len(categorical_columns)}")

# Now run preprocessing
print("\nRunning preprocessing...")
success = preprocess_all_datasets()

if success:
    print("\nPreprocessing completed. Checking results...")
    
    # Check NPY file
    train_features = np.load('processed_data/train_features.npy')
    print(f"NPY shape: {train_features.shape}")
    
    # Calculate expected features
    expected = len(numeric_columns) + len(categorical_columns)
    for col in array_columns:
        expected += analysis_results["column_analysis"][col]["array_length"]
    
    print(f"Expected features: {expected}")
    print(f"Actual features: {train_features.shape[1]}")
    
    if train_features.shape[1] == expected:
        print("\nSUCCESS: Arrays are properly expanded!")
    else:
        print("\nERROR: Arrays were not expanded properly!")
else:
    print("\nPreprocessing failed!")