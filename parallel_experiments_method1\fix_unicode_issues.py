"""Fix Unicode issues in parallel_processor.py"""

import re

def fix_unicode_in_file(filepath):
    """Replace Unicode characters with ASCII equivalents"""
    
    replacements = {
        '✅': '[OK]',
        '❌': '[ERROR]',
        '⚠️': '[WARNING]',
        '📊': '[INFO]',
        '🔧': '[DEBUG]',
        '⏰': '[TIMEOUT]',
        '🚀': '[START]',
        '🎉': '[SUCCESS]'
    }
    
    # Read the file
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Count replacements
    total_replacements = 0
    
    # Replace each Unicode character
    for unicode_char, ascii_replacement in replacements.items():
        count = content.count(unicode_char)
        if count > 0:
            content = content.replace(unicode_char, ascii_replacement)
            total_replacements += count
            print(f"Replaced {count} occurrences with '{ascii_replacement}'")
    
    # Write back
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"\nTotal replacements: {total_replacements}")
    return total_replacements

if __name__ == "__main__":
    print("Fixing Unicode issues in parallel_processor.py...")
    count = fix_unicode_in_file('src/parallel_processor.py')
    
    if count > 0:
        print(f"\n[OK] Fixed {count} Unicode characters in parallel_processor.py")
    else:
        print("\n[INFO] No Unicode characters found to fix")