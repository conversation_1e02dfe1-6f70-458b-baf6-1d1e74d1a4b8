"""Test the integrated preprocessing pipeline without Unicode issues"""

import os
import sys
import subprocess

def test_pipeline(venv_path):
    """Test the integrated pipeline with specified virtual environment"""
    
    # Set UTF-8 encoding for Windows
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['PYTHONLEGACYWINDOWSSTDIO'] = '1'
    
    # Run the pipeline with small dataset
    cmd = [
        venv_path,
        'src/run_parallel_processing.py',
        '--workers', '1',  # Use single worker to avoid multiprocessing Unicode issues
        '--track-only'     # Use track-only mode to reduce output
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    print("="*60)
    
    result = subprocess.run(cmd, env=env, capture_output=True, text=True)
    
    # Print output
    if result.stdout:
        print("STDOUT:")
        print(result.stdout)
    
    if result.stderr:
        print("STDERR:")
        print(result.stderr)
    
    # Check if metadata was generated
    metadata_file = 'processed_data/feature_metadata_expanded.json'
    if os.path.exists(metadata_file):
        print(f"\n[OK] Metadata file generated: {metadata_file}")
        
        # Check metadata content
        import json
        with open(metadata_file, 'r') as f:
            metadata = json.load(f)
        
        print(f"  Total features: {metadata['total_features']}")
        print(f"  Feature groups: {len(metadata['groups'])}")
        print(f"  NPY shape: {metadata.get('npy_shape', 'Not set')}")
        
        # Count array features
        array_features = [f for f in metadata['features'] if f['type'] == 'embedding_element']
        print(f"  Array features expanded: {len(array_features)}")
        
        return True
    else:
        print(f"\n[FAIL] Metadata file not found: {metadata_file}")
        return False

if __name__ == "__main__":
    # First remove existing metadata to test generation
    metadata_file = 'processed_data/feature_metadata_expanded.json'
    if os.path.exists(metadata_file):
        os.rename(metadata_file, metadata_file + '.bak2')
        print(f"Backed up existing metadata file")
    
    # Test with recommendation_venv
    print("\n" + "="*60)
    print("Testing with recommendation_venv...")
    print("="*60)
    
    venv_path = '../recommendation_venv/Scripts/python.exe'
    success = test_pipeline(venv_path)
    
    if success:
        print("\n[OK] Integrated pipeline test passed!")
    else:
        print("\n[FAIL] Integrated pipeline test failed!")
    
    # Restore backup
    if os.path.exists(metadata_file + '.bak2'):
        os.rename(metadata_file + '.bak2', metadata_file)
        print("\nRestored backup metadata file")