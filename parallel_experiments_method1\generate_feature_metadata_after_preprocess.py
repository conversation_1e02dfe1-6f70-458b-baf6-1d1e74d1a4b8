"""
Generate feature metadata after preprocessing to match the actual NPY structure
This should be run AFTER preprocessing to ensure metadata matches the actual data
"""

import os
import json
import pandas as pd
import numpy as np

def generate_feature_metadata_after_preprocess():
    """
    Generate feature metadata based on the actual preprocessed NPY files
    This ensures the metadata exactly matches what's in the NPY files
    """
    
    # First verify NPY files exist
    if not os.path.exists('processed_data/train_features.npy'):
        print("ERROR: NPY files not found. Please run preprocessing first!")
        return None
        
    # Load NPY to get actual dimensions
    train_features = np.load('processed_data/train_features.npy')
    actual_feature_count = train_features.shape[1]
    print(f"Actual NPY shape: {train_features.shape}")
    print(f"Actual feature count: {actual_feature_count}")
    
    # Load data analysis results to understand structure
    with open('data_analysis/data_analysis_results.json', 'r') as f:
        analysis_results = json.load(f)
    
    # Read sample parquet for column names
    sample_file = r"local_test_data\small\train\data_chunk_000.parquet"
    df = pd.read_parquet(sample_file)
    
    # Define excluded columns and label
    label_column = "click"
    excluded_columns = ["user_id", "item_id", "timestamp"]
    
    feature_metadata = {
        'features': [],
        'groups': {},
        'total_features': 0,
        'label_column': label_column,
        'excluded_columns': excluded_columns,
        'original_columns': list(df.columns),
        'npy_shape': list(train_features.shape)
    }
    
    feature_index = 0
    
    # Process numeric columns first
    for col in analysis_results['summary']['numeric_columns']:
        if col == label_column or col in excluded_columns:
            continue
            
        prefix = col.split('_')[0] if '_' in col else ''
        
        if prefix not in feature_metadata['groups']:
            feature_metadata['groups'][prefix] = []
        
        feature_info = {
            'index': feature_index,
            'name': col,
            'type': 'numeric',
            'group': prefix,
            'original_column': col
        }
        
        feature_metadata['features'].append(feature_info)
        feature_metadata['groups'][prefix].append(feature_index)
        feature_index += 1
    
    # Process categorical columns
    for col in analysis_results['summary']['categorical_columns']:
        if col == label_column or col in excluded_columns:
            continue
            
        prefix = col.split('_')[0] if '_' in col else ''
        
        if prefix not in feature_metadata['groups']:
            feature_metadata['groups'][prefix] = []
        
        feature_info = {
            'index': feature_index,
            'name': col,
            'type': 'categorical',
            'group': prefix,
            'original_column': col
        }
        
        feature_metadata['features'].append(feature_info)
        feature_metadata['groups'][prefix].append(feature_index)
        feature_index += 1
    
    # Process array columns (expanded)
    for col in analysis_results['summary']['array_columns']:
        if col == label_column or col in excluded_columns:
            continue
            
        prefix = col.split('_')[0] if '_' in col else ''
        
        if prefix not in feature_metadata['groups']:
            feature_metadata['groups'][prefix] = []
        
        # Get array dimension
        array_dim = analysis_results['column_analysis'][col]['array_length']
        
        # Add each expanded feature
        for i in range(array_dim):
            feature_info = {
                'index': feature_index,
                'name': f"{col}_{i}",
                'type': 'embedding_element',
                'group': prefix,
                'original_column': col,
                'embedding_index': i,
                'embedding_size': array_dim
            }
            
            feature_metadata['features'].append(feature_info)
            feature_metadata['groups'][prefix].append(feature_index)
            feature_index += 1
    
    feature_metadata['total_features'] = feature_index
    
    # Verify the count matches NPY
    if feature_index != actual_feature_count:
        print(f"WARNING: Metadata feature count ({feature_index}) doesn't match NPY ({actual_feature_count})")
        print("This indicates a mismatch between preprocessing and metadata generation!")
        return None
    
    # Save metadata
    os.makedirs('processed_data', exist_ok=True)
    metadata_file = 'processed_data/feature_metadata_expanded.json'
    
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(feature_metadata, f, indent=2, ensure_ascii=False)
    
    print(f"\n✓ Feature metadata saved to: {metadata_file}")
    print(f"Total features: {feature_metadata['total_features']}")
    print(f"\nFeature groups:")
    
    for group, indices in sorted(feature_metadata['groups'].items()):
        group_name = group if group else "(no prefix)"
        print(f"  {group_name}: {len(indices)} features")
    
    # Summary
    print(f"\nFeature breakdown:")
    print(f"  Numeric features: {len(analysis_results['summary']['numeric_columns'])}")
    print(f"  Categorical features: {len(analysis_results['summary']['categorical_columns'])}")
    print(f"  Array features expanded:")
    for col in analysis_results['summary']['array_columns']:
        dim = analysis_results['column_analysis'][col]['array_length']
        print(f"    {col}: {dim} dimensions")
    print(f"\nTotal after expansion: {feature_index} features")
    
    return feature_metadata

if __name__ == "__main__":
    result = generate_feature_metadata_after_preprocess()
    if result is None:
        print("\nERROR: Failed to generate metadata!")
        print("Make sure to:")
        print("1. Run data analysis first")
        print("2. Run preprocessing to generate NPY files")
        print("3. Then run this script")