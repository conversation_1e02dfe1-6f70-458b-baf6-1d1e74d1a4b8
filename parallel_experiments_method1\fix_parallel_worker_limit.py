"""
修复并行处理worker数限制的脚本
用于在EC2环境中允许更多的worker进程
"""

import os
import shutil
from datetime import datetime

def fix_worker_limit():
    """修改parallel_processor.py中的worker数限制"""
    
    # 文件路径
    file_path = "src/parallel_processor.py"
    backup_path = f"src/parallel_processor.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 创建备份
    print(f"创建备份: {backup_path}")
    shutil.copy2(file_path, backup_path)
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找并替换限制代码
    old_limit = """        if self.max_workers > 20:  # 如果worker数过多，限制为20
            self.max_workers = 20"""
    
    # EC2环境可以使用更多worker
    new_limit = """        # EC2环境可以使用更多worker
        if os.getenv('IS_EC2_DEPLOYMENT', 'false').lower() == 'true':
            # EC2环境允许更多worker，但仍然有个合理上限
            if self.max_workers > 64:  # EC2上限制为64
                self.max_workers = 64
                logger.warning(f"🔧 EC2 S3并发优化: 将worker数从 {original_max_workers} 限制为 {self.max_workers}")
        else:
            # 非EC2环境保持原有限制
            if self.max_workers > 20:  # 如果worker数过多，限制为20
                self.max_workers = 20"""
    
    if old_limit in content:
        content = content.replace(old_limit, new_limit)
        print("✅ 成功修改worker数限制逻辑")
        print("   - EC2环境: 最多64个worker")
        print("   - 非EC2环境: 最多20个worker")
    else:
        print("⚠️ 未找到需要修改的代码，可能已经修改过或代码结构不同")
        return False
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"\n✅ 修改完成！")
    print(f"备份文件: {backup_path}")
    print("\n使用方法:")
    print("1. 在EC2上设置环境变量: export IS_EC2_DEPLOYMENT=true")
    print("2. 运行: python src/run_parallel_processing.py --workers 32")
    
    return True

if __name__ == "__main__":
    fix_worker_limit()