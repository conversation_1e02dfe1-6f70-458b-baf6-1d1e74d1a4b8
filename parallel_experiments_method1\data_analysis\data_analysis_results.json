{"datasets": {"train": {"dataset_name": "train", "data_directory": "local_test_data\\small\\train", "num_files": 2, "schema_consistent": true, "estimated_total_rows": 14000, "columns_analysis": {"user_id": {"name": "user_id", "dtype": "int64", "null_count": "0", "null_percentage": 0.0, "unique_count": 350, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 349.0, "mean_value": 174.5, "recommended_dtype": "uint16", "unique_values": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}, "item_id": {"name": "item_id", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 500, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [57, 150, 50, 40, 361], "min_value": 0.0, "max_value": 499.0, "mean_value": 247.96971428571428, "recommended_dtype": "uint16", "unique_values": [57, 150, 50, 40, 361, 467, 490, 153, 82, 465]}, "click": {"name": "click", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 2, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": true, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 1.0, "mean_value": 0.09714285714285714, "recommended_dtype": "uint8", "unique_values": [0, 1]}, "conversion": {"name": "conversion", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 2, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": true, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 1.0, "mean_value": 0.02, "recommended_dtype": "uint8", "unique_values": [0, 1]}, "time_spent": {"name": "time_spent", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [325.1403363087606, 585.2513985330321, 1055.587738224293, 3511.7967330237757, 1181.0515314686627], "min_value": 0.12229657134548155, "max_value": 3599.8062434189883, "mean_value": 1818.6199198518243, "recommended_dtype": "float16", "unique_values": [325.1403363087606, 585.2513985330321, 1055.587738224293, 3511.7967330237757, 1181.0515314686627, 2027.8345823258194, 224.72705799645226, 2463.7152002862886, 2810.0976168824795, 3236.5529123828755]}, "click_position": {"name": "click_position", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [18.632570952840396, 8.14980286275461, 10.550401309343522, 6.279446819519228, 15.47165776963509], "min_value": 1.0036670047456444, "max_value": 19.98779719864549, "mean_value": 10.638912625219016, "recommended_dtype": "float16", "unique_values": [18.632570952840396, 8.14980286275461, 10.550401309343522, 6.279446819519228, 15.47165776963509, 15.464804964996121, 19.83053625837352, 12.573020534244892, 12.404568313464788, 16.014906463380996]}, "session_length": {"name": "session_length", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [34.28449610227012, 68.84123124923839, 93.98727385762173, 7.294295379605025, 10.808494281417273], "min_value": 1.0004445241485338, "max_value": 99.99921390125701, "mean_value": 50.58658560733862, "recommended_dtype": "float16", "unique_values": [34.28449610227012, 68.84123124923839, 93.98727385762173, 7.294295379605025, 10.808494281417273, 9.924486079713006, 58.38558498490511, 85.01412984276277, 87.36133633874279, 97.81631931888911]}, "context_embedding": {"name": "context_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 32, "sample_values": ["[ 0.11469712 -0.0379403   0.10460602 -0.00433257 -0.02756655 -0.01622673\n -0.02932025 -0.08873687  0.02530897  0.17178393 -0.02048766  0.06565764\n  0.10813495  0.02006287 -0.02825421  0.04474462  0.02909453  0.1043238\n  0.0525236   0.09022205 -0.06736901  0.09378922 -0.03140623  0.00714414\n  0.01976534 -0.0682672   0.0138485  -0.00024975  0.07967751 -0.01166211\n -0.08684895  0.15532076]", "[-0.15962868 -0.03014239 -0.10684248  0.07346075  0.19928301  0.10189722\n  0.03986467  0.10104176 -0.11451135 -0.04272621  0.12354302 -0.05968224\n  0.08981835 -0.09490781 -0.0207976  -0.13838483 -0.12787016 -0.14334706\n  0.02311278  0.02866347  0.12627209 -0.1054133  -0.01833312  0.06367221\n -0.01388587 -0.13306334 -0.03904562 -0.10472162 -0.06573719 -0.09828921\n  0.04735459 -0.03046021]", "[-0.06857737 -0.02816682 -0.07925987 -0.16397459 -0.08950583 -0.10954108\n -0.07380601  0.17640554  0.02834309  0.15575757 -0.06465126  0.02197278\n -0.10594004 -0.1656382  -0.03597041 -0.20842154 -0.0578537  -0.1757139\n  0.07044485  0.05513072 -0.20925183  0.00766661 -0.06300113  0.04345153\n  0.00994533  0.05663957 -0.07321293  0.01809494  0.00848981 -0.0267119\n -0.14592807  0.22312135]", "[ 0.16044292 -0.0293886  -0.06370636  0.01147131  0.04781361  0.18683573\n  0.14503328 -0.25369417 -0.08439084 -0.15045601  0.08756065 -0.13263746\n -0.03903813 -0.03896883  0.08478125  0.02222826 -0.10395762  0.12405384\n  0.00082017 -0.04389476 -0.09169149 -0.05461562  0.10118776 -0.122013\n -0.05546364 -0.06079778  0.13719124  0.01967946 -0.17605599 -0.02211314\n -0.1120778  -0.05154859]", "[-0.09366398 -0.21074971  0.12066421 -0.09876824 -0.02374807 -0.13027012\n -0.03452381  0.14299658  0.04231246  0.11894097  0.08813773  0.03785874\n -0.03325721  0.19516386 -0.03815472 -0.23768066 -0.21578732  0.10459258\n -0.04067927 -0.10035176 -0.0354566  -0.06124905  0.04799882  0.02031231\n -0.01097279  0.0136197  -0.0215278   0.01856424 -0.19286663 -0.2342087\n  0.01403251 -0.00561212]"]}, "age": {"name": "age", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 350, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [56.45289778717836, 56.45289778717836, 56.45289778717836, 56.45289778717836, 56.45289778717836], "min_value": 18.37456588825829, "max_value": 79.99246362732238, "mean_value": 49.318663389551006, "recommended_dtype": "float16", "unique_values": [56.45289778717836, 73.92789531010266, 45.32669933027931, 54.44952704604387, 68.60051764758077, 30.233821374411725, 46.75213460224215, 55.05721316240774, 56.39745217971456, 64.34596153449935]}, "gender": {"name": "gender", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 3, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["F", "F", "F", "F", "F"], "recommended_dtype": "object", "unique_values": ["F", "M", "O"]}, "region": {"name": "region", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 20, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["region_13", "region_13", "region_13", "region_13", "region_13"], "recommended_dtype": "object", "unique_values": ["region_13", "region_7", "region_16", "region_1", "region_3", "region_6", "region_9", "region_10", "region_5", "region_4"]}, "income_level": {"name": "income_level", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 4, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["high", "high", "high", "high", "high"], "recommended_dtype": "object", "unique_values": ["high", "very_high", "low", "medium"]}, "user_embedding": {"name": "user_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 64, "sample_values": ["[ 0.1348977   0.11728511 -0.08657819 -0.15393659  0.11553587 -0.03966276\n -0.11772909  0.05009036  0.11300254 -0.09567639 -0.21530402  0.03520525\n  0.07051856  0.04067867 -0.15172176 -0.14616842  0.01745448  0.09122879\n  0.27699815  0.07338267 -0.17141108  0.04523422 -0.05689954 -0.06162627\n  0.06571472 -0.02301197 -0.00316374  0.14043566  0.04942254 -0.10874556\n -0.13895044 -0.11165596 -0.24386113 -0.06792129  0.09889048 -0.08611167\n  0.20272153 -0.01286116  0.05883131  0.01585548 -0.14918071  0.12400409\n -0.10546329 -0.03265693  0.1238584  -0.01612865  0.01397694  0.10081789\n  0.21842882  0.09320086 -0.03688608  0.09411141  0.07964039 -0.16156903\n  0.09975491 -0.07935608  0.07793165  0.00315616  0.09601379  0.17955913\n  0.01964023 -0.0222611  -0.07015406  0.06151006]", "[ 0.1348977   0.11728511 -0.08657819 -0.15393659  0.11553587 -0.03966276\n -0.11772909  0.05009036  0.11300254 -0.09567639 -0.21530402  0.03520525\n  0.07051856  0.04067867 -0.15172176 -0.14616842  0.01745448  0.09122879\n  0.27699815  0.07338267 -0.17141108  0.04523422 -0.05689954 -0.06162627\n  0.06571472 -0.02301197 -0.00316374  0.14043566  0.04942254 -0.10874556\n -0.13895044 -0.11165596 -0.24386113 -0.06792129  0.09889048 -0.08611167\n  0.20272153 -0.01286116  0.05883131  0.01585548 -0.14918071  0.12400409\n -0.10546329 -0.03265693  0.1238584  -0.01612865  0.01397694  0.10081789\n  0.21842882  0.09320086 -0.03688608  0.09411141  0.07964039 -0.16156903\n  0.09975491 -0.07935608  0.07793165  0.00315616  0.09601379  0.17955913\n  0.01964023 -0.0222611  -0.07015406  0.06151006]", "[ 0.1348977   0.11728511 -0.08657819 -0.15393659  0.11553587 -0.03966276\n -0.11772909  0.05009036  0.11300254 -0.09567639 -0.21530402  0.03520525\n  0.07051856  0.04067867 -0.15172176 -0.14616842  0.01745448  0.09122879\n  0.27699815  0.07338267 -0.17141108  0.04523422 -0.05689954 -0.06162627\n  0.06571472 -0.02301197 -0.00316374  0.14043566  0.04942254 -0.10874556\n -0.13895044 -0.11165596 -0.24386113 -0.06792129  0.09889048 -0.08611167\n  0.20272153 -0.01286116  0.05883131  0.01585548 -0.14918071  0.12400409\n -0.10546329 -0.03265693  0.1238584  -0.01612865  0.01397694  0.10081789\n  0.21842882  0.09320086 -0.03688608  0.09411141  0.07964039 -0.16156903\n  0.09975491 -0.07935608  0.07793165  0.00315616  0.09601379  0.17955913\n  0.01964023 -0.0222611  -0.07015406  0.06151006]", "[ 0.1348977   0.11728511 -0.08657819 -0.15393659  0.11553587 -0.03966276\n -0.11772909  0.05009036  0.11300254 -0.09567639 -0.21530402  0.03520525\n  0.07051856  0.04067867 -0.15172176 -0.14616842  0.01745448  0.09122879\n  0.27699815  0.07338267 -0.17141108  0.04523422 -0.05689954 -0.06162627\n  0.06571472 -0.02301197 -0.00316374  0.14043566  0.04942254 -0.10874556\n -0.13895044 -0.11165596 -0.24386113 -0.06792129  0.09889048 -0.08611167\n  0.20272153 -0.01286116  0.05883131  0.01585548 -0.14918071  0.12400409\n -0.10546329 -0.03265693  0.1238584  -0.01612865  0.01397694  0.10081789\n  0.21842882  0.09320086 -0.03688608  0.09411141  0.07964039 -0.16156903\n  0.09975491 -0.07935608  0.07793165  0.00315616  0.09601379  0.17955913\n  0.01964023 -0.0222611  -0.07015406  0.06151006]", "[ 0.1348977   0.11728511 -0.08657819 -0.15393659  0.11553587 -0.03966276\n -0.11772909  0.05009036  0.11300254 -0.09567639 -0.21530402  0.03520525\n  0.07051856  0.04067867 -0.15172176 -0.14616842  0.01745448  0.09122879\n  0.27699815  0.07338267 -0.17141108  0.04523422 -0.05689954 -0.06162627\n  0.06571472 -0.02301197 -0.00316374  0.14043566  0.04942254 -0.10874556\n -0.13895044 -0.11165596 -0.24386113 -0.06792129  0.09889048 -0.08611167\n  0.20272153 -0.01286116  0.05883131  0.01585548 -0.14918071  0.12400409\n -0.10546329 -0.03265693  0.1238584  -0.01612865  0.01397694  0.10081789\n  0.21842882  0.09320086 -0.03688608  0.09411141  0.07964039 -0.16156903\n  0.09975491 -0.07935608  0.07793165  0.00315616  0.09601379  0.17955913\n  0.01964023 -0.0222611  -0.07015406  0.06151006]"]}, "category": {"name": "category", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 10, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["cat_6", "cat_8", "cat_5", "cat_3", "cat_9"], "recommended_dtype": "object", "unique_values": ["cat_6", "cat_8", "cat_5", "cat_3", "cat_9", "cat_0", "cat_1", "cat_2", "cat_7", "cat_4"]}, "price": {"name": "price", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 500, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [3.397369684301219, 767.2554976390817, 638.7458502508905, 151.8837785458814, 807.9442392844821], "min_value": 1.756018129078533, "max_value": 999.2101141834955, "mean_value": 474.98482607066035, "recommended_dtype": "float16", "unique_values": [3.397369684301219, 767.2554976390817, 638.7458502508905, 151.8837785458814, 807.9442392844821, 131.22841801120924, 723.8161420322702, 838.4943047378699, 31.158298624451405, 331.3411196237183]}, "brand": {"name": "brand", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 50, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["brand_13", "brand_30", "brand_5", "brand_33", "brand_4"], "recommended_dtype": "object", "unique_values": ["brand_13", "brand_30", "brand_5", "brand_33", "brand_4", "brand_34", "brand_49", "brand_21", "brand_14", "brand_12"]}, "rating": {"name": "rating", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 500, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [2.129040264213769, 2.0613438167154015, 4.583648486300001, 1.6463939258723492, 3.6308292205000545], "min_value": 1.0146035123110448, "max_value": 4.9925715069283445, "mean_value": 2.9805096036809617, "recommended_dtype": "float16", "unique_values": [2.129040264213769, 2.0613438167154015, 4.583648486300001, 1.6463939258723492, 3.6308292205000545, 2.921719060195919, 2.5450597622637963, 3.9774096058774697, 3.307838578583649, 1.2991173303223058]}, "item_embedding": {"name": "item_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 64, "sample_values": ["[ 0.08275681 -0.10619595  0.10987962 -0.15626178  0.04234023 -0.16954769\n  0.05950662  0.08035324 -0.0466414   0.07089154 -0.1219609   0.12494512\n  0.00283981  0.08796134  0.08798992 -0.04595601  0.08827223  0.11448756\n -0.11708118 -0.05928836  0.07195318  0.03152784 -0.08534121  0.02391859\n -0.13026866 -0.14740625 -0.04610124 -0.04943272 -0.13199137 -0.12946072\n -0.18608392  0.00079328  0.00776194 -0.05209563 -0.15108027 -0.03027547\n  0.03663764  0.04217204  0.07802855 -0.06426546  0.0017589   0.01519328\n -0.12247712  0.09484731  0.10232834 -0.15121972  0.12692034 -0.0038121\n  0.06924292  0.17353063  0.03388073 -0.06249147  0.05296721 -0.08854868\n -0.10583512  0.05438096  0.15521617  0.02172254 -0.01533456  0.14460811\n  0.10265879  0.03096776 -0.19336959  0.02161175]", "[-0.11365766  0.13401495  0.1027696   0.14277927 -0.17834875 -0.06845632\n -0.15221278 -0.06255816 -0.0118693  -0.01040173  0.00148914  0.12550667\n  0.03429572 -0.01915907  0.03213577  0.17456758 -0.02817182 -0.11784169\n  0.15284637  0.01043087  0.08627197  0.17648446 -0.04387895  0.09449384\n  0.08661245 -0.08859712 -0.11324562  0.04662125 -0.10817704  0.12511264\n -0.03265461 -0.09712131  0.0027621   0.01311718 -0.0938207  -0.03117503\n  0.20640485 -0.00337635 -0.06505324  0.22319803  0.13180559 -0.02167674\n  0.01984548 -0.0851213  -0.05015314  0.00934077 -0.20844032  0.01640757\n  0.09961951  0.11665223 -0.05842134 -0.06315399  0.03869307 -0.14934282\n -0.06672174  0.01377918 -0.06426471 -0.06077741 -0.12944331  0.28550047\n  0.02962349  0.03398486 -0.02789957 -0.0230966 ]", "[-8.69908180e-02 -4.07759741e-02  6.64131657e-02 -5.79904172e-02\n  9.28353431e-03 -1.35043025e-01  7.98339174e-05  1.84906621e-02\n -4.90266991e-02 -2.35236705e-01 -8.64531591e-02  2.59715069e-01\n  5.92190127e-02 -5.68437087e-02 -5.02141404e-02 -6.16417861e-02\n  5.85095448e-02  6.02248478e-02 -6.60056856e-02 -1.14041286e-01\n -8.73852496e-02 -1.67545200e-01 -4.87227603e-02 -4.25718689e-02\n  4.58601098e-02 -6.03882904e-02 -1.41716830e-01  1.19431363e-02\n  9.46726874e-02  1.13863410e-01  1.30394107e-01  2.00893274e-02\n -1.70958788e-01 -3.41476282e-02  9.75156137e-02  3.16827218e-02\n  1.03666214e-01 -1.87491363e-02  4.73205651e-02 -4.26041776e-03\n -7.74194869e-03  6.66344484e-03 -5.94776937e-02  7.77206927e-02\n -2.81047129e-02 -5.94738194e-02  8.22512480e-02 -9.95003976e-02\n  5.88829522e-02  3.46978537e-02 -1.57372289e-02  4.36739424e-02\n  1.01153883e-01  1.18136317e-01 -1.89247823e-01 -1.37845068e-01\n -4.55530388e-03  3.99498605e-02 -8.04750353e-03 -1.72170301e-02\n -3.37400238e-02 -1.02498653e-01 -8.80136551e-02  2.61728283e-02]", "[ 0.0332438   0.01945661  0.12860899 -0.0124792   0.01840764 -0.0271837\n  0.03512089  0.02580387  0.16349201 -0.02626807 -0.02065433 -0.30431175\n -0.0617446  -0.05254792 -0.004037   -0.08955773  0.01710305 -0.0775331\n  0.02795877  0.08250304 -0.11004687 -0.17143342 -0.0075674  -0.13859553\n  0.11049303 -0.08818586 -0.02413503 -0.01936742 -0.09851405  0.09492442\n  0.08736771  0.05995139  0.0838317  -0.09816704  0.11369668  0.0151941\n  0.00788374 -0.06160983 -0.01193087 -0.07494015 -0.00115653 -0.00419132\n -0.00072433 -0.03108164 -0.15765256 -0.17451872 -0.01597524 -0.06636662\n  0.15399998 -0.00804891  0.16348609 -0.00643171 -0.04102526  0.28816571\n -0.20535034 -0.08916388  0.21186041 -0.09100852 -0.02728556 -0.06129227\n -0.00547071 -0.0128906  -0.06242964 -0.06029499]", "[ 0.07079132  0.04380905  0.11719171  0.07567016 -0.03479687 -0.18257096\n -0.09954716 -0.03924205 -0.10977474  0.24026753 -0.04850084  0.06464905\n -0.07415337 -0.01453089  0.11526398  0.02478369 -0.00318174 -0.01134536\n -0.01317328 -0.00775769 -0.24437243 -0.00994181  0.34528042 -0.02959789\n -0.05899768  0.2181762   0.00992923  0.06095042  0.13101833 -0.01768611\n  0.04902348  0.09964296  0.16288063  0.23223919 -0.13793655 -0.03370978\n -0.10871413  0.17313829 -0.12622844 -0.11831532  0.06460157 -0.03736764\n -0.0751778  -0.0094691   0.0107799  -0.09268846  0.09422077  0.13216657\n  0.10000533  0.1906421   0.09841099  0.0010731  -0.05933834  0.03738515\n -0.08854521  0.05795309  0.03715591  0.15654603 -0.20606319 -0.00840523\n  0.08299299  0.07682752  0.09998002 -0.02189757]"]}, "noise_1": {"name": "noise_1", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [0.7617040813322526, -0.23555568062321397, -0.43864192126065815, -2.6104042028578625, -0.5623561783212478], "min_value": -3.8199888608274115, "max_value": 3.4026220856488614, "mean_value": 0.003394795393516045, "recommended_dtype": "float16", "unique_values": [0.7617040813322526, -0.23555568062321397, -0.43864192126065815, -2.6104042028578625, -0.5623561783212478, 0.9531488714809622, 0.3135911364293218, -0.2664915637394834, 1.3002463593347289, -0.597864756961923]}, "noise_2": {"name": "noise_2", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 3, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["A", "B", "A", "C", "C"], "recommended_dtype": "object", "unique_values": ["A", "B", "C"]}, "timestamp": {"name": "timestamp", "dtype": "datetime64[ns]", "null_count": "0", "null_percentage": 0.0, "unique_count": 6990, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["2025-07-21 03:59:47.223384", "2025-06-26 08:48:40.223384", "2025-07-16 18:43:33.223384", "2025-06-29 00:39:56.223384", "2025-06-27 03:33:13.223384"], "recommended_dtype": "datetime64[ns]", "unique_values": ["2025-07-21 03:59:47.223384", "2025-06-26 08:48:40.223384", "2025-07-16 18:43:33.223384", "2025-06-29 00:39:56.223384", "2025-06-27 03:33:13.223384", "2025-07-17 02:33:35.223384", "2025-07-07 12:23:28.223384", "2025-07-06 14:12:07.223384", "2025-07-13 07:12:31.223384", "2025-07-01 17:29:39.223384"]}}, "label_column": "click", "array_columns": ["context_embedding", "user_embedding", "item_embedding"], "numeric_columns": ["user_id", "item_id", "click", "conversion", "time_spent", "click_position", "session_length", "age", "price", "rating", "noise_1"], "categorical_columns": ["gender", "region", "income_level", "category", "brand", "noise_2", "timestamp"], "sample_file_analysis": {"file_path": "local_test_data\\small\\train\\data_chunk_000.parquet", "total_rows": 7000, "total_columns": 21, "columns": {"user_id": {"name": "user_id", "dtype": "int64", "null_count": "0", "null_percentage": 0.0, "unique_count": 350, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 349.0, "mean_value": 174.5, "recommended_dtype": "uint16", "unique_values": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}, "item_id": {"name": "item_id", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 500, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [57, 150, 50, 40, 361], "min_value": 0.0, "max_value": 499.0, "mean_value": 247.96971428571428, "recommended_dtype": "uint16", "unique_values": [57, 150, 50, 40, 361, 467, 490, 153, 82, 465]}, "click": {"name": "click", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 2, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": true, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 1.0, "mean_value": 0.09714285714285714, "recommended_dtype": "uint8", "unique_values": [0, 1]}, "conversion": {"name": "conversion", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 2, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": true, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 1.0, "mean_value": 0.02, "recommended_dtype": "uint8", "unique_values": [0, 1]}, "time_spent": {"name": "time_spent", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [325.1403363087606, 585.2513985330321, 1055.587738224293, 3511.7967330237757, 1181.0515314686627], "min_value": 0.12229657134548155, "max_value": 3599.8062434189883, "mean_value": 1818.6199198518243, "recommended_dtype": "float16", "unique_values": [325.1403363087606, 585.2513985330321, 1055.587738224293, 3511.7967330237757, 1181.0515314686627, 2027.8345823258194, 224.72705799645226, 2463.7152002862886, 2810.0976168824795, 3236.5529123828755]}, "click_position": {"name": "click_position", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [18.632570952840396, 8.14980286275461, 10.550401309343522, 6.279446819519228, 15.47165776963509], "min_value": 1.0036670047456444, "max_value": 19.98779719864549, "mean_value": 10.638912625219016, "recommended_dtype": "float16", "unique_values": [18.632570952840396, 8.14980286275461, 10.550401309343522, 6.279446819519228, 15.47165776963509, 15.464804964996121, 19.83053625837352, 12.573020534244892, 12.404568313464788, 16.014906463380996]}, "session_length": {"name": "session_length", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [34.28449610227012, 68.84123124923839, 93.98727385762173, 7.294295379605025, 10.808494281417273], "min_value": 1.0004445241485338, "max_value": 99.99921390125701, "mean_value": 50.58658560733862, "recommended_dtype": "float16", "unique_values": [34.28449610227012, 68.84123124923839, 93.98727385762173, 7.294295379605025, 10.808494281417273, 9.924486079713006, 58.38558498490511, 85.01412984276277, 87.36133633874279, 97.81631931888911]}, "context_embedding": {"name": "context_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 32, "sample_values": ["[ 0.11469712 -0.0379403   0.10460602 -0.00433257 -0.02756655 -0.01622673\n -0.02932025 -0.08873687  0.02530897  0.17178393 -0.02048766  0.06565764\n  0.10813495  0.02006287 -0.02825421  0.04474462  0.02909453  0.1043238\n  0.0525236   0.09022205 -0.06736901  0.09378922 -0.03140623  0.00714414\n  0.01976534 -0.0682672   0.0138485  -0.00024975  0.07967751 -0.01166211\n -0.08684895  0.15532076]", "[-0.15962868 -0.03014239 -0.10684248  0.07346075  0.19928301  0.10189722\n  0.03986467  0.10104176 -0.11451135 -0.04272621  0.12354302 -0.05968224\n  0.08981835 -0.09490781 -0.0207976  -0.13838483 -0.12787016 -0.14334706\n  0.02311278  0.02866347  0.12627209 -0.1054133  -0.01833312  0.06367221\n -0.01388587 -0.13306334 -0.03904562 -0.10472162 -0.06573719 -0.09828921\n  0.04735459 -0.03046021]", "[-0.06857737 -0.02816682 -0.07925987 -0.16397459 -0.08950583 -0.10954108\n -0.07380601  0.17640554  0.02834309  0.15575757 -0.06465126  0.02197278\n -0.10594004 -0.1656382  -0.03597041 -0.20842154 -0.0578537  -0.1757139\n  0.07044485  0.05513072 -0.20925183  0.00766661 -0.06300113  0.04345153\n  0.00994533  0.05663957 -0.07321293  0.01809494  0.00848981 -0.0267119\n -0.14592807  0.22312135]", "[ 0.16044292 -0.0293886  -0.06370636  0.01147131  0.04781361  0.18683573\n  0.14503328 -0.25369417 -0.08439084 -0.15045601  0.08756065 -0.13263746\n -0.03903813 -0.03896883  0.08478125  0.02222826 -0.10395762  0.12405384\n  0.00082017 -0.04389476 -0.09169149 -0.05461562  0.10118776 -0.122013\n -0.05546364 -0.06079778  0.13719124  0.01967946 -0.17605599 -0.02211314\n -0.1120778  -0.05154859]", "[-0.09366398 -0.21074971  0.12066421 -0.09876824 -0.02374807 -0.13027012\n -0.03452381  0.14299658  0.04231246  0.11894097  0.08813773  0.03785874\n -0.03325721  0.19516386 -0.03815472 -0.23768066 -0.21578732  0.10459258\n -0.04067927 -0.10035176 -0.0354566  -0.06124905  0.04799882  0.02031231\n -0.01097279  0.0136197  -0.0215278   0.01856424 -0.19286663 -0.2342087\n  0.01403251 -0.00561212]"]}, "age": {"name": "age", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 350, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [56.45289778717836, 56.45289778717836, 56.45289778717836, 56.45289778717836, 56.45289778717836], "min_value": 18.37456588825829, "max_value": 79.99246362732238, "mean_value": 49.318663389551006, "recommended_dtype": "float16", "unique_values": [56.45289778717836, 73.92789531010266, 45.32669933027931, 54.44952704604387, 68.60051764758077, 30.233821374411725, 46.75213460224215, 55.05721316240774, 56.39745217971456, 64.34596153449935]}, "gender": {"name": "gender", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 3, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["F", "F", "F", "F", "F"], "recommended_dtype": "object", "unique_values": ["F", "M", "O"]}, "region": {"name": "region", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 20, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["region_13", "region_13", "region_13", "region_13", "region_13"], "recommended_dtype": "object", "unique_values": ["region_13", "region_7", "region_16", "region_1", "region_3", "region_6", "region_9", "region_10", "region_5", "region_4"]}, "income_level": {"name": "income_level", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 4, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["high", "high", "high", "high", "high"], "recommended_dtype": "object", "unique_values": ["high", "very_high", "low", "medium"]}, "user_embedding": {"name": "user_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 64, "sample_values": ["[ 0.1348977   0.11728511 -0.08657819 -0.15393659  0.11553587 -0.03966276\n -0.11772909  0.05009036  0.11300254 -0.09567639 -0.21530402  0.03520525\n  0.07051856  0.04067867 -0.15172176 -0.14616842  0.01745448  0.09122879\n  0.27699815  0.07338267 -0.17141108  0.04523422 -0.05689954 -0.06162627\n  0.06571472 -0.02301197 -0.00316374  0.14043566  0.04942254 -0.10874556\n -0.13895044 -0.11165596 -0.24386113 -0.06792129  0.09889048 -0.08611167\n  0.20272153 -0.01286116  0.05883131  0.01585548 -0.14918071  0.12400409\n -0.10546329 -0.03265693  0.1238584  -0.01612865  0.01397694  0.10081789\n  0.21842882  0.09320086 -0.03688608  0.09411141  0.07964039 -0.16156903\n  0.09975491 -0.07935608  0.07793165  0.00315616  0.09601379  0.17955913\n  0.01964023 -0.0222611  -0.07015406  0.06151006]", "[ 0.1348977   0.11728511 -0.08657819 -0.15393659  0.11553587 -0.03966276\n -0.11772909  0.05009036  0.11300254 -0.09567639 -0.21530402  0.03520525\n  0.07051856  0.04067867 -0.15172176 -0.14616842  0.01745448  0.09122879\n  0.27699815  0.07338267 -0.17141108  0.04523422 -0.05689954 -0.06162627\n  0.06571472 -0.02301197 -0.00316374  0.14043566  0.04942254 -0.10874556\n -0.13895044 -0.11165596 -0.24386113 -0.06792129  0.09889048 -0.08611167\n  0.20272153 -0.01286116  0.05883131  0.01585548 -0.14918071  0.12400409\n -0.10546329 -0.03265693  0.1238584  -0.01612865  0.01397694  0.10081789\n  0.21842882  0.09320086 -0.03688608  0.09411141  0.07964039 -0.16156903\n  0.09975491 -0.07935608  0.07793165  0.00315616  0.09601379  0.17955913\n  0.01964023 -0.0222611  -0.07015406  0.06151006]", "[ 0.1348977   0.11728511 -0.08657819 -0.15393659  0.11553587 -0.03966276\n -0.11772909  0.05009036  0.11300254 -0.09567639 -0.21530402  0.03520525\n  0.07051856  0.04067867 -0.15172176 -0.14616842  0.01745448  0.09122879\n  0.27699815  0.07338267 -0.17141108  0.04523422 -0.05689954 -0.06162627\n  0.06571472 -0.02301197 -0.00316374  0.14043566  0.04942254 -0.10874556\n -0.13895044 -0.11165596 -0.24386113 -0.06792129  0.09889048 -0.08611167\n  0.20272153 -0.01286116  0.05883131  0.01585548 -0.14918071  0.12400409\n -0.10546329 -0.03265693  0.1238584  -0.01612865  0.01397694  0.10081789\n  0.21842882  0.09320086 -0.03688608  0.09411141  0.07964039 -0.16156903\n  0.09975491 -0.07935608  0.07793165  0.00315616  0.09601379  0.17955913\n  0.01964023 -0.0222611  -0.07015406  0.06151006]", "[ 0.1348977   0.11728511 -0.08657819 -0.15393659  0.11553587 -0.03966276\n -0.11772909  0.05009036  0.11300254 -0.09567639 -0.21530402  0.03520525\n  0.07051856  0.04067867 -0.15172176 -0.14616842  0.01745448  0.09122879\n  0.27699815  0.07338267 -0.17141108  0.04523422 -0.05689954 -0.06162627\n  0.06571472 -0.02301197 -0.00316374  0.14043566  0.04942254 -0.10874556\n -0.13895044 -0.11165596 -0.24386113 -0.06792129  0.09889048 -0.08611167\n  0.20272153 -0.01286116  0.05883131  0.01585548 -0.14918071  0.12400409\n -0.10546329 -0.03265693  0.1238584  -0.01612865  0.01397694  0.10081789\n  0.21842882  0.09320086 -0.03688608  0.09411141  0.07964039 -0.16156903\n  0.09975491 -0.07935608  0.07793165  0.00315616  0.09601379  0.17955913\n  0.01964023 -0.0222611  -0.07015406  0.06151006]", "[ 0.1348977   0.11728511 -0.08657819 -0.15393659  0.11553587 -0.03966276\n -0.11772909  0.05009036  0.11300254 -0.09567639 -0.21530402  0.03520525\n  0.07051856  0.04067867 -0.15172176 -0.14616842  0.01745448  0.09122879\n  0.27699815  0.07338267 -0.17141108  0.04523422 -0.05689954 -0.06162627\n  0.06571472 -0.02301197 -0.00316374  0.14043566  0.04942254 -0.10874556\n -0.13895044 -0.11165596 -0.24386113 -0.06792129  0.09889048 -0.08611167\n  0.20272153 -0.01286116  0.05883131  0.01585548 -0.14918071  0.12400409\n -0.10546329 -0.03265693  0.1238584  -0.01612865  0.01397694  0.10081789\n  0.21842882  0.09320086 -0.03688608  0.09411141  0.07964039 -0.16156903\n  0.09975491 -0.07935608  0.07793165  0.00315616  0.09601379  0.17955913\n  0.01964023 -0.0222611  -0.07015406  0.06151006]"]}, "category": {"name": "category", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 10, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["cat_6", "cat_8", "cat_5", "cat_3", "cat_9"], "recommended_dtype": "object", "unique_values": ["cat_6", "cat_8", "cat_5", "cat_3", "cat_9", "cat_0", "cat_1", "cat_2", "cat_7", "cat_4"]}, "price": {"name": "price", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 500, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [3.397369684301219, 767.2554976390817, 638.7458502508905, 151.8837785458814, 807.9442392844821], "min_value": 1.756018129078533, "max_value": 999.2101141834955, "mean_value": 474.98482607066035, "recommended_dtype": "float16", "unique_values": [3.397369684301219, 767.2554976390817, 638.7458502508905, 151.8837785458814, 807.9442392844821, 131.22841801120924, 723.8161420322702, 838.4943047378699, 31.158298624451405, 331.3411196237183]}, "brand": {"name": "brand", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 50, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["brand_13", "brand_30", "brand_5", "brand_33", "brand_4"], "recommended_dtype": "object", "unique_values": ["brand_13", "brand_30", "brand_5", "brand_33", "brand_4", "brand_34", "brand_49", "brand_21", "brand_14", "brand_12"]}, "rating": {"name": "rating", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 500, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [2.129040264213769, 2.0613438167154015, 4.583648486300001, 1.6463939258723492, 3.6308292205000545], "min_value": 1.0146035123110448, "max_value": 4.9925715069283445, "mean_value": 2.9805096036809617, "recommended_dtype": "float16", "unique_values": [2.129040264213769, 2.0613438167154015, 4.583648486300001, 1.6463939258723492, 3.6308292205000545, 2.921719060195919, 2.5450597622637963, 3.9774096058774697, 3.307838578583649, 1.2991173303223058]}, "item_embedding": {"name": "item_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 64, "sample_values": ["[ 0.08275681 -0.10619595  0.10987962 -0.15626178  0.04234023 -0.16954769\n  0.05950662  0.08035324 -0.0466414   0.07089154 -0.1219609   0.12494512\n  0.00283981  0.08796134  0.08798992 -0.04595601  0.08827223  0.11448756\n -0.11708118 -0.05928836  0.07195318  0.03152784 -0.08534121  0.02391859\n -0.13026866 -0.14740625 -0.04610124 -0.04943272 -0.13199137 -0.12946072\n -0.18608392  0.00079328  0.00776194 -0.05209563 -0.15108027 -0.03027547\n  0.03663764  0.04217204  0.07802855 -0.06426546  0.0017589   0.01519328\n -0.12247712  0.09484731  0.10232834 -0.15121972  0.12692034 -0.0038121\n  0.06924292  0.17353063  0.03388073 -0.06249147  0.05296721 -0.08854868\n -0.10583512  0.05438096  0.15521617  0.02172254 -0.01533456  0.14460811\n  0.10265879  0.03096776 -0.19336959  0.02161175]", "[-0.11365766  0.13401495  0.1027696   0.14277927 -0.17834875 -0.06845632\n -0.15221278 -0.06255816 -0.0118693  -0.01040173  0.00148914  0.12550667\n  0.03429572 -0.01915907  0.03213577  0.17456758 -0.02817182 -0.11784169\n  0.15284637  0.01043087  0.08627197  0.17648446 -0.04387895  0.09449384\n  0.08661245 -0.08859712 -0.11324562  0.04662125 -0.10817704  0.12511264\n -0.03265461 -0.09712131  0.0027621   0.01311718 -0.0938207  -0.03117503\n  0.20640485 -0.00337635 -0.06505324  0.22319803  0.13180559 -0.02167674\n  0.01984548 -0.0851213  -0.05015314  0.00934077 -0.20844032  0.01640757\n  0.09961951  0.11665223 -0.05842134 -0.06315399  0.03869307 -0.14934282\n -0.06672174  0.01377918 -0.06426471 -0.06077741 -0.12944331  0.28550047\n  0.02962349  0.03398486 -0.02789957 -0.0230966 ]", "[-8.69908180e-02 -4.07759741e-02  6.64131657e-02 -5.79904172e-02\n  9.28353431e-03 -1.35043025e-01  7.98339174e-05  1.84906621e-02\n -4.90266991e-02 -2.35236705e-01 -8.64531591e-02  2.59715069e-01\n  5.92190127e-02 -5.68437087e-02 -5.02141404e-02 -6.16417861e-02\n  5.85095448e-02  6.02248478e-02 -6.60056856e-02 -1.14041286e-01\n -8.73852496e-02 -1.67545200e-01 -4.87227603e-02 -4.25718689e-02\n  4.58601098e-02 -6.03882904e-02 -1.41716830e-01  1.19431363e-02\n  9.46726874e-02  1.13863410e-01  1.30394107e-01  2.00893274e-02\n -1.70958788e-01 -3.41476282e-02  9.75156137e-02  3.16827218e-02\n  1.03666214e-01 -1.87491363e-02  4.73205651e-02 -4.26041776e-03\n -7.74194869e-03  6.66344484e-03 -5.94776937e-02  7.77206927e-02\n -2.81047129e-02 -5.94738194e-02  8.22512480e-02 -9.95003976e-02\n  5.88829522e-02  3.46978537e-02 -1.57372289e-02  4.36739424e-02\n  1.01153883e-01  1.18136317e-01 -1.89247823e-01 -1.37845068e-01\n -4.55530388e-03  3.99498605e-02 -8.04750353e-03 -1.72170301e-02\n -3.37400238e-02 -1.02498653e-01 -8.80136551e-02  2.61728283e-02]", "[ 0.0332438   0.01945661  0.12860899 -0.0124792   0.01840764 -0.0271837\n  0.03512089  0.02580387  0.16349201 -0.02626807 -0.02065433 -0.30431175\n -0.0617446  -0.05254792 -0.004037   -0.08955773  0.01710305 -0.0775331\n  0.02795877  0.08250304 -0.11004687 -0.17143342 -0.0075674  -0.13859553\n  0.11049303 -0.08818586 -0.02413503 -0.01936742 -0.09851405  0.09492442\n  0.08736771  0.05995139  0.0838317  -0.09816704  0.11369668  0.0151941\n  0.00788374 -0.06160983 -0.01193087 -0.07494015 -0.00115653 -0.00419132\n -0.00072433 -0.03108164 -0.15765256 -0.17451872 -0.01597524 -0.06636662\n  0.15399998 -0.00804891  0.16348609 -0.00643171 -0.04102526  0.28816571\n -0.20535034 -0.08916388  0.21186041 -0.09100852 -0.02728556 -0.06129227\n -0.00547071 -0.0128906  -0.06242964 -0.06029499]", "[ 0.07079132  0.04380905  0.11719171  0.07567016 -0.03479687 -0.18257096\n -0.09954716 -0.03924205 -0.10977474  0.24026753 -0.04850084  0.06464905\n -0.07415337 -0.01453089  0.11526398  0.02478369 -0.00318174 -0.01134536\n -0.01317328 -0.00775769 -0.24437243 -0.00994181  0.34528042 -0.02959789\n -0.05899768  0.2181762   0.00992923  0.06095042  0.13101833 -0.01768611\n  0.04902348  0.09964296  0.16288063  0.23223919 -0.13793655 -0.03370978\n -0.10871413  0.17313829 -0.12622844 -0.11831532  0.06460157 -0.03736764\n -0.0751778  -0.0094691   0.0107799  -0.09268846  0.09422077  0.13216657\n  0.10000533  0.1906421   0.09841099  0.0010731  -0.05933834  0.03738515\n -0.08854521  0.05795309  0.03715591  0.15654603 -0.20606319 -0.00840523\n  0.08299299  0.07682752  0.09998002 -0.02189757]"]}, "noise_1": {"name": "noise_1", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 7000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [0.7617040813322526, -0.23555568062321397, -0.43864192126065815, -2.6104042028578625, -0.5623561783212478], "min_value": -3.8199888608274115, "max_value": 3.4026220856488614, "mean_value": 0.003394795393516045, "recommended_dtype": "float16", "unique_values": [0.7617040813322526, -0.23555568062321397, -0.43864192126065815, -2.6104042028578625, -0.5623561783212478, 0.9531488714809622, 0.3135911364293218, -0.2664915637394834, 1.3002463593347289, -0.597864756961923]}, "noise_2": {"name": "noise_2", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 3, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["A", "B", "A", "C", "C"], "recommended_dtype": "object", "unique_values": ["A", "B", "C"]}, "timestamp": {"name": "timestamp", "dtype": "datetime64[ns]", "null_count": "0", "null_percentage": 0.0, "unique_count": 6990, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["2025-07-21 03:59:47.223384", "2025-06-26 08:48:40.223384", "2025-07-16 18:43:33.223384", "2025-06-29 00:39:56.223384", "2025-06-27 03:33:13.223384"], "recommended_dtype": "datetime64[ns]", "unique_values": ["2025-07-21 03:59:47.223384", "2025-06-26 08:48:40.223384", "2025-07-16 18:43:33.223384", "2025-06-29 00:39:56.223384", "2025-06-27 03:33:13.223384", "2025-07-17 02:33:35.223384", "2025-07-07 12:23:28.223384", "2025-07-06 14:12:07.223384", "2025-07-13 07:12:31.223384", "2025-07-01 17:29:39.223384"]}}, "label_column": "click", "array_columns": ["context_embedding", "user_embedding", "item_embedding"], "numeric_columns": ["user_id", "item_id", "click", "conversion", "time_spent", "click_position", "session_length", "age", "price", "rating", "noise_1"], "categorical_columns": ["gender", "region", "income_level", "category", "brand", "noise_2", "timestamp"]}}, "validation": {"dataset_name": "validation", "data_directory": "local_test_data\\small\\validation", "num_files": 2, "schema_consistent": true, "estimated_total_rows": 4000, "columns_analysis": {"user_id": {"name": "user_id", "dtype": "int64", "null_count": "0", "null_percentage": 0.0, "unique_count": 100, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [700, 700, 700, 700, 700], "min_value": 700.0, "max_value": 799.0, "mean_value": 749.5, "recommended_dtype": "uint16", "unique_values": [700, 701, 702, 703, 704, 705, 706, 707, 708, 709]}, "item_id": {"name": "item_id", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 494, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [165, 41, 32, 9, 460], "min_value": 0.0, "max_value": 499.0, "mean_value": 247.702, "recommended_dtype": "uint16", "unique_values": [165, 41, 32, 9, 460, 27, 101, 259, 33, 135]}, "click": {"name": "click", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 2, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": true, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 1.0, "mean_value": 0.0945, "recommended_dtype": "uint8", "unique_values": [0, 1]}, "conversion": {"name": "conversion", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 2, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": true, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 1.0, "mean_value": 0.0195, "recommended_dtype": "uint8", "unique_values": [0, 1]}, "time_spent": {"name": "time_spent", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [1694.0704244861245, 1364.64049824165, 1935.321529566038, 1054.0641579940175, 1725.3068438386076], "min_value": 0.24578556204262192, "max_value": 3599.242890123873, "mean_value": 1806.9237048029927, "recommended_dtype": "float16", "unique_values": [1694.0704244861245, 1364.64049824165, 1935.321529566038, 1054.0641579940175, 1725.3068438386076, 1975.0692931966696, 2544.262103925634, 980.4159316110891, 1676.986229902105, 2899.6552857716447]}, "click_position": {"name": "click_position", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [3.906026736788066, 6.887013012780795, 9.15421234890761, 13.77495919474371, 12.838474253356882], "min_value": 1.0073787809467556, "max_value": 19.995282283559092, "mean_value": 10.532532696716162, "recommended_dtype": "float16", "unique_values": [3.906026736788066, 6.887013012780795, 9.15421234890761, 13.77495919474371, 12.838474253356882, 6.578574164606766, 8.524158962691196, 16.34893378466849, 15.075380265656033, 3.7061878484741553]}, "session_length": {"name": "session_length", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [14.075657149606924, 50.23787316370301, 51.71598485987041, 68.47865470006994, 2.3175287327138894], "min_value": 1.0138192188402817, "max_value": 99.97985908239882, "mean_value": 50.76277271488841, "recommended_dtype": "float16", "unique_values": [14.075657149606924, 50.23787316370301, 51.71598485987041, 68.47865470006994, 2.3175287327138894, 39.78750678779498, 98.85133160934548, 14.247409588444793, 3.131666662019615, 40.1752390996056]}, "context_embedding": {"name": "context_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 32, "sample_values": ["[ 0.28242777 -0.18269629  0.0531332   0.10986134  0.18369092  0.0156299\n  0.12165529  0.11780826 -0.10697095 -0.01029966  0.22770107 -0.06343281\n  0.08131002  0.06273083 -0.22874381 -0.26545697 -0.03102337 -0.09574314\n  0.10014299 -0.03804288  0.03954945 -0.13815683  0.03321203  0.01758942\n -0.09738749  0.23834775 -0.10592134 -0.19378232  0.10259952 -0.01242654\n  0.09178314 -0.17163728]", "[ 0.06326376 -0.0911389   0.14091811  0.09422468  0.13417087  0.07858916\n -0.05261963 -0.31179311  0.04636941 -0.2153999  -0.08581555 -0.14353303\n -0.13003347 -0.05547481 -0.00678198  0.02483839  0.09363767 -0.08512756\n  0.02431471  0.17322628 -0.00458269 -0.13817729 -0.17098655 -0.01967228\n  0.07925971 -0.07278321 -0.09161174  0.00653245  0.10348115  0.08192878\n  0.04549194 -0.07155991]", "[ 0.1669173   0.07418732  0.01858155  0.18613504  0.17916327  0.00921683\n -0.06421883  0.01884648  0.05532119 -0.05343326 -0.19424521  0.01040368\n  0.03519133 -0.10951815 -0.00476825 -0.08836246  0.13519947  0.12869417\n  0.09700236  0.02899615  0.00778162  0.05756     0.09511846  0.0810894\n -0.1222768  -0.0915166   0.038852   -0.13547884  0.00725661 -0.08063724\n -0.03242802  0.0017604 ]", "[ 6.31295088e-02  5.45550390e-02 -9.44030334e-02  3.24250029e-02\n -6.16712836e-02 -1.99373744e-03 -2.72859463e-02  1.09811230e-02\n -3.20508004e-02  1.98123705e-03  6.79520354e-06  2.71695516e-02\n -1.86040769e-01 -2.12162700e-01  2.99869878e-02 -1.18874443e-01\n -3.59934280e-02  1.61476883e-02 -4.66438775e-03 -2.40566840e-02\n -2.03637778e-01 -1.23601271e-01 -7.50841198e-02 -4.72542295e-02\n -6.48077824e-02 -1.26639568e-01 -4.30400133e-02 -1.11231731e-01\n -7.27669253e-04 -9.54105528e-02  2.56030173e-01  3.08268044e-02]", "[-0.04435463  0.16615612 -0.07763814  0.07515619 -0.02524234 -0.0999088\n  0.07770648 -0.21172292 -0.13602341  0.00116249  0.10454239  0.03376645\n  0.01951525  0.10854332 -0.11026974 -0.05412584 -0.00839286  0.01429067\n -0.09035792  0.00444655 -0.04536492 -0.02325829  0.06433503  0.00674394\n  0.06879754  0.11649278  0.04702124 -0.00602925 -0.05188292  0.06863245\n  0.09322507  0.02091264]"]}, "age": {"name": "age", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 100, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [77.8323944459647, 77.8323944459647, 77.8323944459647, 77.8323944459647, 77.8323944459647], "min_value": 18.092163122052394, "max_value": 79.87344144111846, "mean_value": 49.0063866631531, "recommended_dtype": "float16", "unique_values": [77.8323944459647, 48.45022348833363, 46.18483783284324, 34.87100716145697, 78.01816509118026, 67.58822084481247, 55.956944495333886, 46.90203224989797, 76.39714657446416, 36.27461511959787]}, "gender": {"name": "gender", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 3, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["M", "M", "M", "M", "M"], "recommended_dtype": "object", "unique_values": ["M", "O", "F"]}, "region": {"name": "region", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 19, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["region_1", "region_1", "region_1", "region_1", "region_1"], "recommended_dtype": "object", "unique_values": ["region_1", "region_13", "region_9", "region_15", "region_16", "region_14", "region_3", "region_6", "region_19", "region_17"]}, "income_level": {"name": "income_level", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 4, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["very_high", "very_high", "very_high", "very_high", "very_high"], "recommended_dtype": "object", "unique_values": ["very_high", "high", "low", "medium"]}, "user_embedding": {"name": "user_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 64, "sample_values": ["[-0.22692924  0.06938314  0.2376743  -0.00469088 -0.14532728 -0.00407359\n -0.20789426  0.10562588 -0.0509729  -0.12029154  0.14368773 -0.12832293\n  0.02556911  0.16559811 -0.13601076  0.04879964  0.22413223  0.03942218\n  0.05639001 -0.0474216   0.00783666  0.03346098  0.14518031  0.27972631\n -0.04466807  0.11164354  0.02154142 -0.10721906 -0.13831336 -0.13975983\n -0.1048563  -0.05319754  0.06767794 -0.0091734  -0.05227269  0.02390735\n -0.12067553  0.04253017  0.03964607  0.03388851 -0.14693175  0.35243931\n -0.12460034  0.05562964 -0.09793239 -0.08033369 -0.01594178  0.07364084\n -0.06073733 -0.13675019 -0.01173888  0.11246177  0.03674268  0.02512594\n -0.00913368  0.04944318 -0.05844555  0.15995725 -0.01358102  0.08254226\n  0.17759028 -0.18848113 -0.00976126 -0.03770903]", "[-0.22692924  0.06938314  0.2376743  -0.00469088 -0.14532728 -0.00407359\n -0.20789426  0.10562588 -0.0509729  -0.12029154  0.14368773 -0.12832293\n  0.02556911  0.16559811 -0.13601076  0.04879964  0.22413223  0.03942218\n  0.05639001 -0.0474216   0.00783666  0.03346098  0.14518031  0.27972631\n -0.04466807  0.11164354  0.02154142 -0.10721906 -0.13831336 -0.13975983\n -0.1048563  -0.05319754  0.06767794 -0.0091734  -0.05227269  0.02390735\n -0.12067553  0.04253017  0.03964607  0.03388851 -0.14693175  0.35243931\n -0.12460034  0.05562964 -0.09793239 -0.08033369 -0.01594178  0.07364084\n -0.06073733 -0.13675019 -0.01173888  0.11246177  0.03674268  0.02512594\n -0.00913368  0.04944318 -0.05844555  0.15995725 -0.01358102  0.08254226\n  0.17759028 -0.18848113 -0.00976126 -0.03770903]", "[-0.22692924  0.06938314  0.2376743  -0.00469088 -0.14532728 -0.00407359\n -0.20789426  0.10562588 -0.0509729  -0.12029154  0.14368773 -0.12832293\n  0.02556911  0.16559811 -0.13601076  0.04879964  0.22413223  0.03942218\n  0.05639001 -0.0474216   0.00783666  0.03346098  0.14518031  0.27972631\n -0.04466807  0.11164354  0.02154142 -0.10721906 -0.13831336 -0.13975983\n -0.1048563  -0.05319754  0.06767794 -0.0091734  -0.05227269  0.02390735\n -0.12067553  0.04253017  0.03964607  0.03388851 -0.14693175  0.35243931\n -0.12460034  0.05562964 -0.09793239 -0.08033369 -0.01594178  0.07364084\n -0.06073733 -0.13675019 -0.01173888  0.11246177  0.03674268  0.02512594\n -0.00913368  0.04944318 -0.05844555  0.15995725 -0.01358102  0.08254226\n  0.17759028 -0.18848113 -0.00976126 -0.03770903]", "[-0.22692924  0.06938314  0.2376743  -0.00469088 -0.14532728 -0.00407359\n -0.20789426  0.10562588 -0.0509729  -0.12029154  0.14368773 -0.12832293\n  0.02556911  0.16559811 -0.13601076  0.04879964  0.22413223  0.03942218\n  0.05639001 -0.0474216   0.00783666  0.03346098  0.14518031  0.27972631\n -0.04466807  0.11164354  0.02154142 -0.10721906 -0.13831336 -0.13975983\n -0.1048563  -0.05319754  0.06767794 -0.0091734  -0.05227269  0.02390735\n -0.12067553  0.04253017  0.03964607  0.03388851 -0.14693175  0.35243931\n -0.12460034  0.05562964 -0.09793239 -0.08033369 -0.01594178  0.07364084\n -0.06073733 -0.13675019 -0.01173888  0.11246177  0.03674268  0.02512594\n -0.00913368  0.04944318 -0.05844555  0.15995725 -0.01358102  0.08254226\n  0.17759028 -0.18848113 -0.00976126 -0.03770903]", "[-0.22692924  0.06938314  0.2376743  -0.00469088 -0.14532728 -0.00407359\n -0.20789426  0.10562588 -0.0509729  -0.12029154  0.14368773 -0.12832293\n  0.02556911  0.16559811 -0.13601076  0.04879964  0.22413223  0.03942218\n  0.05639001 -0.0474216   0.00783666  0.03346098  0.14518031  0.27972631\n -0.04466807  0.11164354  0.02154142 -0.10721906 -0.13831336 -0.13975983\n -0.1048563  -0.05319754  0.06767794 -0.0091734  -0.05227269  0.02390735\n -0.12067553  0.04253017  0.03964607  0.03388851 -0.14693175  0.35243931\n -0.12460034  0.05562964 -0.09793239 -0.08033369 -0.01594178  0.07364084\n -0.06073733 -0.13675019 -0.01173888  0.11246177  0.03674268  0.02512594\n -0.00913368  0.04944318 -0.05844555  0.15995725 -0.01358102  0.08254226\n  0.17759028 -0.18848113 -0.00976126 -0.03770903]"]}, "category": {"name": "category", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 10, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["cat_0", "cat_3", "cat_6", "cat_7", "cat_1"], "recommended_dtype": "object", "unique_values": ["cat_0", "cat_3", "cat_6", "cat_7", "cat_1", "cat_9", "cat_4", "cat_8", "cat_2", "cat_5"]}, "price": {"name": "price", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 494, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [117.68777886188185, 169.75379450807654, 788.3102518678445, 103.35991449814023, 741.9315606609447], "min_value": 1.756018129078533, "max_value": 999.2101141834955, "mean_value": 481.57573240936006, "recommended_dtype": "float16", "unique_values": [117.68777886188185, 169.75379450807654, 788.3102518678445, 103.35991449814023, 741.9315606609447, 823.7818749114849, 77.12013311631605, 58.277374662374704, 975.4532723380578, 533.379674589393]}, "brand": {"name": "brand", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 50, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["brand_29", "brand_0", "brand_8", "brand_27", "brand_22"], "recommended_dtype": "object", "unique_values": ["brand_29", "brand_0", "brand_8", "brand_27", "brand_22", "brand_34", "brand_26", "brand_39", "brand_9", "brand_16"]}, "rating": {"name": "rating", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 494, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [2.3470965560801003, 4.378779823559581, 4.532494286618041, 4.487703357359559, 4.381811149307938], "min_value": 1.0146035123110448, "max_value": 4.9925715069283445, "mean_value": 2.9619845298696053, "recommended_dtype": "float16", "unique_values": [2.3470965560801003, 4.378779823559581, 4.532494286618041, 4.487703357359559, 4.381811149307938, 4.224938494550627, 2.7614200418175177, 1.978310544611435, 3.338164374410687, 4.653766312372619]}, "item_embedding": {"name": "item_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 64, "sample_values": ["[ 0.06780709 -0.11566523  0.1108039  -0.01669391 -0.03872867 -0.19874936\n -0.13067384 -0.0277405   0.3123763   0.06203867  0.18008197  0.2718759\n -0.14391679  0.14420875 -0.07173784  0.03740163  0.04088824 -0.13224671\n  0.02860395 -0.03826256 -0.03315727 -0.06551517 -0.16743312  0.08070612\n -0.02096497 -0.02860743 -0.17876095 -0.1224289  -0.03571761 -0.0353394\n -0.08374337  0.20580337 -0.04265522  0.02891815  0.14295597  0.07330521\n -0.12082904 -0.03675571  0.11313249 -0.08176481  0.04227656 -0.02793167\n  0.06182404 -0.04540243 -0.07939012  0.04936271  0.07318591 -0.09455136\n  0.0130673   0.04758293  0.21580074 -0.13894241  0.17186655  0.04280917\n  0.03062967 -0.03252097 -0.17046089  0.0629686  -0.05657344  0.04030071\n  0.03769981  0.06152482 -0.06196749 -0.13949686]", "[-0.01297161 -0.03599595  0.01393115  0.00457425 -0.24701603  0.0640768\n  0.04423264  0.10522248 -0.14841285  0.14383958 -0.03114862  0.02113802\n -0.11505887 -0.04609732  0.0396634   0.20470239 -0.04729377 -0.04391661\n -0.1503256  -0.18504197  0.1203892   0.21403389  0.07088471  0.12303324\n -0.03644952 -0.04562002 -0.13130868  0.25502769  0.08276053 -0.11244034\n  0.03865485  0.12492625 -0.00852621 -0.05030189  0.04512074 -0.12970536\n -0.11238344  0.00811139  0.04221873  0.0235766  -0.13608908  0.05859185\n -0.03481287  0.0232686   0.00305647  0.1454773  -0.20951844  0.08548461\n -0.08746169  0.00914177 -0.0233543   0.22262499  0.00058904  0.14375596\n -0.09073873  0.20576607  0.08299785  0.08066749  0.07756685  0.08421574\n -0.07088839 -0.15934012  0.18110636  0.17188649]", "[-0.03560129  0.09294951 -0.02754352  0.21161808  0.07171218 -0.10829878\n  0.00500295  0.03942825  0.08174459  0.04079903  0.13053948  0.08478645\n -0.00144361 -0.01511588  0.01107102  0.0444724   0.13021939 -0.08393604\n -0.23545757  0.03890523 -0.21354611  0.03784434  0.07243197  0.04283979\n -0.01748195  0.02714202  0.00484703 -0.07951851  0.04065141 -0.12824517\n -0.01230654  0.06881275 -0.07680406 -0.04648117  0.03691115  0.1165445\n  0.0311774  -0.01905972 -0.04162229  0.09738412  0.04185929  0.09323058\n  0.02877086  0.0878579  -0.01078521  0.05023689 -0.05227797 -0.05977488\n  0.03083109 -0.32031016 -0.08140075 -0.04225811 -0.01107914 -0.0160812\n  0.08014606 -0.10430346 -0.05796203  0.00951346 -0.09974996 -0.19844362\n -0.0905719   0.12757955 -0.03539699 -0.07939511]", "[-0.05678056  0.14663561 -0.02313405  0.0596572  -0.07530145 -0.23958356\n -0.05151261 -0.02503063 -0.05744931 -0.14554682 -0.13836559 -0.06511622\n -0.03708463  0.01047484  0.04270542 -0.13766439  0.1144333   0.01365046\n -0.06502819  0.14389745 -0.08045548 -0.06262638  0.20409986  0.08281777\n -0.12116027 -0.00902877  0.17850775  0.03565778 -0.01378391  0.14996266\n -0.07479673  0.01878296  0.06068191  0.05579365  0.01196705  0.1588707\n  0.20880522  0.02327294  0.06782598 -0.1631163  -0.03127612 -0.00223221\n  0.0930596   0.06871431  0.07217275  0.04951628 -0.01725315 -0.12690828\n  0.01479617  0.0810091   0.16121568 -0.02366489 -0.03151599  0.06639025\n  0.08704664 -0.14384647  0.09115951 -0.03656156 -0.05257125  0.09821279\n -0.1398203   0.04496192  0.01817196 -0.1445571 ]", "[-0.18290304  0.05219338 -0.01055462  0.01102652  0.12825305  0.12175282\n  0.14255442  0.05525786 -0.02865179 -0.11400872  0.16562234 -0.12668769\n -0.00046717 -0.19935396 -0.14592636  0.05651491 -0.07274512  0.00621516\n  0.01537226 -0.01043796  0.03591811  0.13158513 -0.16999275 -0.09152422\n  0.04576425 -0.09532104 -0.08623953  0.05493033  0.18887274  0.03975563\n  0.12487045  0.10761367  0.03450892  0.15849096 -0.03756925  0.10489349\n -0.14033225 -0.01599049  0.02580853 -0.01113551 -0.11467444 -0.26236158\n  0.08100782 -0.11674412  0.00346921  0.18237844  0.04285942 -0.02914576\n  0.26908824  0.03669167  0.00676316 -0.01968951 -0.09688728  0.01031669\n  0.01747021  0.17088642 -0.13042223 -0.05704067  0.10196681  0.06788695\n  0.04059321 -0.14054608 -0.22256348 -0.01113826]"]}, "noise_1": {"name": "noise_1", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [-0.035720109426002136, 0.19787146530281388, -0.22000509279588415, -0.2683185991572323, -0.820165641020841], "min_value": -3.3576594606811665, "max_value": 3.263864119669428, "mean_value": -0.03472840541233973, "recommended_dtype": "float16", "unique_values": [-0.035720109426002136, 0.19787146530281388, -0.22000509279588415, -0.2683185991572323, -0.820165641020841, -0.019555670112903593, 0.0003498407154268457, -1.6635583869657764, -0.15940946899556538, -0.09606249032688424]}, "noise_2": {"name": "noise_2", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 3, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["B", "C", "C", "A", "B"], "recommended_dtype": "object", "unique_values": ["B", "C", "A"]}, "timestamp": {"name": "timestamp", "dtype": "datetime64[ns]", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["2025-06-27 09:21:21.223384", "2025-07-07 13:23:31.223384", "2025-07-12 20:15:39.223384", "2025-07-13 02:12:30.223384", "2025-06-30 18:00:22.223384"], "recommended_dtype": "datetime64[ns]", "unique_values": ["2025-06-27 09:21:21.223384", "2025-07-07 13:23:31.223384", "2025-07-12 20:15:39.223384", "2025-07-13 02:12:30.223384", "2025-06-30 18:00:22.223384", "2025-07-13 16:01:32.223384", "2025-07-17 14:38:09.223384", "2025-06-25 19:15:51.223384", "2025-07-18 21:43:04.223384", "2025-07-04 17:55:22.223384"]}}, "label_column": "click", "array_columns": ["context_embedding", "user_embedding", "item_embedding"], "numeric_columns": ["user_id", "item_id", "click", "conversion", "time_spent", "click_position", "session_length", "age", "price", "rating", "noise_1"], "categorical_columns": ["gender", "region", "income_level", "category", "brand", "noise_2", "timestamp"], "sample_file_analysis": {"file_path": "local_test_data\\small\\validation\\data_chunk_000.parquet", "total_rows": 2000, "total_columns": 21, "columns": {"user_id": {"name": "user_id", "dtype": "int64", "null_count": "0", "null_percentage": 0.0, "unique_count": 100, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [700, 700, 700, 700, 700], "min_value": 700.0, "max_value": 799.0, "mean_value": 749.5, "recommended_dtype": "uint16", "unique_values": [700, 701, 702, 703, 704, 705, 706, 707, 708, 709]}, "item_id": {"name": "item_id", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 494, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [165, 41, 32, 9, 460], "min_value": 0.0, "max_value": 499.0, "mean_value": 247.702, "recommended_dtype": "uint16", "unique_values": [165, 41, 32, 9, 460, 27, 101, 259, 33, 135]}, "click": {"name": "click", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 2, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": true, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 1.0, "mean_value": 0.0945, "recommended_dtype": "uint8", "unique_values": [0, 1]}, "conversion": {"name": "conversion", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 2, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": true, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 1.0, "mean_value": 0.0195, "recommended_dtype": "uint8", "unique_values": [0, 1]}, "time_spent": {"name": "time_spent", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [1694.0704244861245, 1364.64049824165, 1935.321529566038, 1054.0641579940175, 1725.3068438386076], "min_value": 0.24578556204262192, "max_value": 3599.242890123873, "mean_value": 1806.9237048029927, "recommended_dtype": "float16", "unique_values": [1694.0704244861245, 1364.64049824165, 1935.321529566038, 1054.0641579940175, 1725.3068438386076, 1975.0692931966696, 2544.262103925634, 980.4159316110891, 1676.986229902105, 2899.6552857716447]}, "click_position": {"name": "click_position", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [3.906026736788066, 6.887013012780795, 9.15421234890761, 13.77495919474371, 12.838474253356882], "min_value": 1.0073787809467556, "max_value": 19.995282283559092, "mean_value": 10.532532696716162, "recommended_dtype": "float16", "unique_values": [3.906026736788066, 6.887013012780795, 9.15421234890761, 13.77495919474371, 12.838474253356882, 6.578574164606766, 8.524158962691196, 16.34893378466849, 15.075380265656033, 3.7061878484741553]}, "session_length": {"name": "session_length", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [14.075657149606924, 50.23787316370301, 51.71598485987041, 68.47865470006994, 2.3175287327138894], "min_value": 1.0138192188402817, "max_value": 99.97985908239882, "mean_value": 50.76277271488841, "recommended_dtype": "float16", "unique_values": [14.075657149606924, 50.23787316370301, 51.71598485987041, 68.47865470006994, 2.3175287327138894, 39.78750678779498, 98.85133160934548, 14.247409588444793, 3.131666662019615, 40.1752390996056]}, "context_embedding": {"name": "context_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 32, "sample_values": ["[ 0.28242777 -0.18269629  0.0531332   0.10986134  0.18369092  0.0156299\n  0.12165529  0.11780826 -0.10697095 -0.01029966  0.22770107 -0.06343281\n  0.08131002  0.06273083 -0.22874381 -0.26545697 -0.03102337 -0.09574314\n  0.10014299 -0.03804288  0.03954945 -0.13815683  0.03321203  0.01758942\n -0.09738749  0.23834775 -0.10592134 -0.19378232  0.10259952 -0.01242654\n  0.09178314 -0.17163728]", "[ 0.06326376 -0.0911389   0.14091811  0.09422468  0.13417087  0.07858916\n -0.05261963 -0.31179311  0.04636941 -0.2153999  -0.08581555 -0.14353303\n -0.13003347 -0.05547481 -0.00678198  0.02483839  0.09363767 -0.08512756\n  0.02431471  0.17322628 -0.00458269 -0.13817729 -0.17098655 -0.01967228\n  0.07925971 -0.07278321 -0.09161174  0.00653245  0.10348115  0.08192878\n  0.04549194 -0.07155991]", "[ 0.1669173   0.07418732  0.01858155  0.18613504  0.17916327  0.00921683\n -0.06421883  0.01884648  0.05532119 -0.05343326 -0.19424521  0.01040368\n  0.03519133 -0.10951815 -0.00476825 -0.08836246  0.13519947  0.12869417\n  0.09700236  0.02899615  0.00778162  0.05756     0.09511846  0.0810894\n -0.1222768  -0.0915166   0.038852   -0.13547884  0.00725661 -0.08063724\n -0.03242802  0.0017604 ]", "[ 6.31295088e-02  5.45550390e-02 -9.44030334e-02  3.24250029e-02\n -6.16712836e-02 -1.99373744e-03 -2.72859463e-02  1.09811230e-02\n -3.20508004e-02  1.98123705e-03  6.79520354e-06  2.71695516e-02\n -1.86040769e-01 -2.12162700e-01  2.99869878e-02 -1.18874443e-01\n -3.59934280e-02  1.61476883e-02 -4.66438775e-03 -2.40566840e-02\n -2.03637778e-01 -1.23601271e-01 -7.50841198e-02 -4.72542295e-02\n -6.48077824e-02 -1.26639568e-01 -4.30400133e-02 -1.11231731e-01\n -7.27669253e-04 -9.54105528e-02  2.56030173e-01  3.08268044e-02]", "[-0.04435463  0.16615612 -0.07763814  0.07515619 -0.02524234 -0.0999088\n  0.07770648 -0.21172292 -0.13602341  0.00116249  0.10454239  0.03376645\n  0.01951525  0.10854332 -0.11026974 -0.05412584 -0.00839286  0.01429067\n -0.09035792  0.00444655 -0.04536492 -0.02325829  0.06433503  0.00674394\n  0.06879754  0.11649278  0.04702124 -0.00602925 -0.05188292  0.06863245\n  0.09322507  0.02091264]"]}, "age": {"name": "age", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 100, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [77.8323944459647, 77.8323944459647, 77.8323944459647, 77.8323944459647, 77.8323944459647], "min_value": 18.092163122052394, "max_value": 79.87344144111846, "mean_value": 49.0063866631531, "recommended_dtype": "float16", "unique_values": [77.8323944459647, 48.45022348833363, 46.18483783284324, 34.87100716145697, 78.01816509118026, 67.58822084481247, 55.956944495333886, 46.90203224989797, 76.39714657446416, 36.27461511959787]}, "gender": {"name": "gender", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 3, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["M", "M", "M", "M", "M"], "recommended_dtype": "object", "unique_values": ["M", "O", "F"]}, "region": {"name": "region", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 19, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["region_1", "region_1", "region_1", "region_1", "region_1"], "recommended_dtype": "object", "unique_values": ["region_1", "region_13", "region_9", "region_15", "region_16", "region_14", "region_3", "region_6", "region_19", "region_17"]}, "income_level": {"name": "income_level", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 4, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["very_high", "very_high", "very_high", "very_high", "very_high"], "recommended_dtype": "object", "unique_values": ["very_high", "high", "low", "medium"]}, "user_embedding": {"name": "user_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 64, "sample_values": ["[-0.22692924  0.06938314  0.2376743  -0.00469088 -0.14532728 -0.00407359\n -0.20789426  0.10562588 -0.0509729  -0.12029154  0.14368773 -0.12832293\n  0.02556911  0.16559811 -0.13601076  0.04879964  0.22413223  0.03942218\n  0.05639001 -0.0474216   0.00783666  0.03346098  0.14518031  0.27972631\n -0.04466807  0.11164354  0.02154142 -0.10721906 -0.13831336 -0.13975983\n -0.1048563  -0.05319754  0.06767794 -0.0091734  -0.05227269  0.02390735\n -0.12067553  0.04253017  0.03964607  0.03388851 -0.14693175  0.35243931\n -0.12460034  0.05562964 -0.09793239 -0.08033369 -0.01594178  0.07364084\n -0.06073733 -0.13675019 -0.01173888  0.11246177  0.03674268  0.02512594\n -0.00913368  0.04944318 -0.05844555  0.15995725 -0.01358102  0.08254226\n  0.17759028 -0.18848113 -0.00976126 -0.03770903]", "[-0.22692924  0.06938314  0.2376743  -0.00469088 -0.14532728 -0.00407359\n -0.20789426  0.10562588 -0.0509729  -0.12029154  0.14368773 -0.12832293\n  0.02556911  0.16559811 -0.13601076  0.04879964  0.22413223  0.03942218\n  0.05639001 -0.0474216   0.00783666  0.03346098  0.14518031  0.27972631\n -0.04466807  0.11164354  0.02154142 -0.10721906 -0.13831336 -0.13975983\n -0.1048563  -0.05319754  0.06767794 -0.0091734  -0.05227269  0.02390735\n -0.12067553  0.04253017  0.03964607  0.03388851 -0.14693175  0.35243931\n -0.12460034  0.05562964 -0.09793239 -0.08033369 -0.01594178  0.07364084\n -0.06073733 -0.13675019 -0.01173888  0.11246177  0.03674268  0.02512594\n -0.00913368  0.04944318 -0.05844555  0.15995725 -0.01358102  0.08254226\n  0.17759028 -0.18848113 -0.00976126 -0.03770903]", "[-0.22692924  0.06938314  0.2376743  -0.00469088 -0.14532728 -0.00407359\n -0.20789426  0.10562588 -0.0509729  -0.12029154  0.14368773 -0.12832293\n  0.02556911  0.16559811 -0.13601076  0.04879964  0.22413223  0.03942218\n  0.05639001 -0.0474216   0.00783666  0.03346098  0.14518031  0.27972631\n -0.04466807  0.11164354  0.02154142 -0.10721906 -0.13831336 -0.13975983\n -0.1048563  -0.05319754  0.06767794 -0.0091734  -0.05227269  0.02390735\n -0.12067553  0.04253017  0.03964607  0.03388851 -0.14693175  0.35243931\n -0.12460034  0.05562964 -0.09793239 -0.08033369 -0.01594178  0.07364084\n -0.06073733 -0.13675019 -0.01173888  0.11246177  0.03674268  0.02512594\n -0.00913368  0.04944318 -0.05844555  0.15995725 -0.01358102  0.08254226\n  0.17759028 -0.18848113 -0.00976126 -0.03770903]", "[-0.22692924  0.06938314  0.2376743  -0.00469088 -0.14532728 -0.00407359\n -0.20789426  0.10562588 -0.0509729  -0.12029154  0.14368773 -0.12832293\n  0.02556911  0.16559811 -0.13601076  0.04879964  0.22413223  0.03942218\n  0.05639001 -0.0474216   0.00783666  0.03346098  0.14518031  0.27972631\n -0.04466807  0.11164354  0.02154142 -0.10721906 -0.13831336 -0.13975983\n -0.1048563  -0.05319754  0.06767794 -0.0091734  -0.05227269  0.02390735\n -0.12067553  0.04253017  0.03964607  0.03388851 -0.14693175  0.35243931\n -0.12460034  0.05562964 -0.09793239 -0.08033369 -0.01594178  0.07364084\n -0.06073733 -0.13675019 -0.01173888  0.11246177  0.03674268  0.02512594\n -0.00913368  0.04944318 -0.05844555  0.15995725 -0.01358102  0.08254226\n  0.17759028 -0.18848113 -0.00976126 -0.03770903]", "[-0.22692924  0.06938314  0.2376743  -0.00469088 -0.14532728 -0.00407359\n -0.20789426  0.10562588 -0.0509729  -0.12029154  0.14368773 -0.12832293\n  0.02556911  0.16559811 -0.13601076  0.04879964  0.22413223  0.03942218\n  0.05639001 -0.0474216   0.00783666  0.03346098  0.14518031  0.27972631\n -0.04466807  0.11164354  0.02154142 -0.10721906 -0.13831336 -0.13975983\n -0.1048563  -0.05319754  0.06767794 -0.0091734  -0.05227269  0.02390735\n -0.12067553  0.04253017  0.03964607  0.03388851 -0.14693175  0.35243931\n -0.12460034  0.05562964 -0.09793239 -0.08033369 -0.01594178  0.07364084\n -0.06073733 -0.13675019 -0.01173888  0.11246177  0.03674268  0.02512594\n -0.00913368  0.04944318 -0.05844555  0.15995725 -0.01358102  0.08254226\n  0.17759028 -0.18848113 -0.00976126 -0.03770903]"]}, "category": {"name": "category", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 10, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["cat_0", "cat_3", "cat_6", "cat_7", "cat_1"], "recommended_dtype": "object", "unique_values": ["cat_0", "cat_3", "cat_6", "cat_7", "cat_1", "cat_9", "cat_4", "cat_8", "cat_2", "cat_5"]}, "price": {"name": "price", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 494, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [117.68777886188185, 169.75379450807654, 788.3102518678445, 103.35991449814023, 741.9315606609447], "min_value": 1.756018129078533, "max_value": 999.2101141834955, "mean_value": 481.57573240936006, "recommended_dtype": "float16", "unique_values": [117.68777886188185, 169.75379450807654, 788.3102518678445, 103.35991449814023, 741.9315606609447, 823.7818749114849, 77.12013311631605, 58.277374662374704, 975.4532723380578, 533.379674589393]}, "brand": {"name": "brand", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 50, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["brand_29", "brand_0", "brand_8", "brand_27", "brand_22"], "recommended_dtype": "object", "unique_values": ["brand_29", "brand_0", "brand_8", "brand_27", "brand_22", "brand_34", "brand_26", "brand_39", "brand_9", "brand_16"]}, "rating": {"name": "rating", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 494, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [2.3470965560801003, 4.378779823559581, 4.532494286618041, 4.487703357359559, 4.381811149307938], "min_value": 1.0146035123110448, "max_value": 4.9925715069283445, "mean_value": 2.9619845298696053, "recommended_dtype": "float16", "unique_values": [2.3470965560801003, 4.378779823559581, 4.532494286618041, 4.487703357359559, 4.381811149307938, 4.224938494550627, 2.7614200418175177, 1.978310544611435, 3.338164374410687, 4.653766312372619]}, "item_embedding": {"name": "item_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 64, "sample_values": ["[ 0.06780709 -0.11566523  0.1108039  -0.01669391 -0.03872867 -0.19874936\n -0.13067384 -0.0277405   0.3123763   0.06203867  0.18008197  0.2718759\n -0.14391679  0.14420875 -0.07173784  0.03740163  0.04088824 -0.13224671\n  0.02860395 -0.03826256 -0.03315727 -0.06551517 -0.16743312  0.08070612\n -0.02096497 -0.02860743 -0.17876095 -0.1224289  -0.03571761 -0.0353394\n -0.08374337  0.20580337 -0.04265522  0.02891815  0.14295597  0.07330521\n -0.12082904 -0.03675571  0.11313249 -0.08176481  0.04227656 -0.02793167\n  0.06182404 -0.04540243 -0.07939012  0.04936271  0.07318591 -0.09455136\n  0.0130673   0.04758293  0.21580074 -0.13894241  0.17186655  0.04280917\n  0.03062967 -0.03252097 -0.17046089  0.0629686  -0.05657344  0.04030071\n  0.03769981  0.06152482 -0.06196749 -0.13949686]", "[-0.01297161 -0.03599595  0.01393115  0.00457425 -0.24701603  0.0640768\n  0.04423264  0.10522248 -0.14841285  0.14383958 -0.03114862  0.02113802\n -0.11505887 -0.04609732  0.0396634   0.20470239 -0.04729377 -0.04391661\n -0.1503256  -0.18504197  0.1203892   0.21403389  0.07088471  0.12303324\n -0.03644952 -0.04562002 -0.13130868  0.25502769  0.08276053 -0.11244034\n  0.03865485  0.12492625 -0.00852621 -0.05030189  0.04512074 -0.12970536\n -0.11238344  0.00811139  0.04221873  0.0235766  -0.13608908  0.05859185\n -0.03481287  0.0232686   0.00305647  0.1454773  -0.20951844  0.08548461\n -0.08746169  0.00914177 -0.0233543   0.22262499  0.00058904  0.14375596\n -0.09073873  0.20576607  0.08299785  0.08066749  0.07756685  0.08421574\n -0.07088839 -0.15934012  0.18110636  0.17188649]", "[-0.03560129  0.09294951 -0.02754352  0.21161808  0.07171218 -0.10829878\n  0.00500295  0.03942825  0.08174459  0.04079903  0.13053948  0.08478645\n -0.00144361 -0.01511588  0.01107102  0.0444724   0.13021939 -0.08393604\n -0.23545757  0.03890523 -0.21354611  0.03784434  0.07243197  0.04283979\n -0.01748195  0.02714202  0.00484703 -0.07951851  0.04065141 -0.12824517\n -0.01230654  0.06881275 -0.07680406 -0.04648117  0.03691115  0.1165445\n  0.0311774  -0.01905972 -0.04162229  0.09738412  0.04185929  0.09323058\n  0.02877086  0.0878579  -0.01078521  0.05023689 -0.05227797 -0.05977488\n  0.03083109 -0.32031016 -0.08140075 -0.04225811 -0.01107914 -0.0160812\n  0.08014606 -0.10430346 -0.05796203  0.00951346 -0.09974996 -0.19844362\n -0.0905719   0.12757955 -0.03539699 -0.07939511]", "[-0.05678056  0.14663561 -0.02313405  0.0596572  -0.07530145 -0.23958356\n -0.05151261 -0.02503063 -0.05744931 -0.14554682 -0.13836559 -0.06511622\n -0.03708463  0.01047484  0.04270542 -0.13766439  0.1144333   0.01365046\n -0.06502819  0.14389745 -0.08045548 -0.06262638  0.20409986  0.08281777\n -0.12116027 -0.00902877  0.17850775  0.03565778 -0.01378391  0.14996266\n -0.07479673  0.01878296  0.06068191  0.05579365  0.01196705  0.1588707\n  0.20880522  0.02327294  0.06782598 -0.1631163  -0.03127612 -0.00223221\n  0.0930596   0.06871431  0.07217275  0.04951628 -0.01725315 -0.12690828\n  0.01479617  0.0810091   0.16121568 -0.02366489 -0.03151599  0.06639025\n  0.08704664 -0.14384647  0.09115951 -0.03656156 -0.05257125  0.09821279\n -0.1398203   0.04496192  0.01817196 -0.1445571 ]", "[-0.18290304  0.05219338 -0.01055462  0.01102652  0.12825305  0.12175282\n  0.14255442  0.05525786 -0.02865179 -0.11400872  0.16562234 -0.12668769\n -0.00046717 -0.19935396 -0.14592636  0.05651491 -0.07274512  0.00621516\n  0.01537226 -0.01043796  0.03591811  0.13158513 -0.16999275 -0.09152422\n  0.04576425 -0.09532104 -0.08623953  0.05493033  0.18887274  0.03975563\n  0.12487045  0.10761367  0.03450892  0.15849096 -0.03756925  0.10489349\n -0.14033225 -0.01599049  0.02580853 -0.01113551 -0.11467444 -0.26236158\n  0.08100782 -0.11674412  0.00346921  0.18237844  0.04285942 -0.02914576\n  0.26908824  0.03669167  0.00676316 -0.01968951 -0.09688728  0.01031669\n  0.01747021  0.17088642 -0.13042223 -0.05704067  0.10196681  0.06788695\n  0.04059321 -0.14054608 -0.22256348 -0.01113826]"]}, "noise_1": {"name": "noise_1", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [-0.035720109426002136, 0.19787146530281388, -0.22000509279588415, -0.2683185991572323, -0.820165641020841], "min_value": -3.3576594606811665, "max_value": 3.263864119669428, "mean_value": -0.03472840541233973, "recommended_dtype": "float16", "unique_values": [-0.035720109426002136, 0.19787146530281388, -0.22000509279588415, -0.2683185991572323, -0.820165641020841, -0.019555670112903593, 0.0003498407154268457, -1.6635583869657764, -0.15940946899556538, -0.09606249032688424]}, "noise_2": {"name": "noise_2", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 3, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["B", "C", "C", "A", "B"], "recommended_dtype": "object", "unique_values": ["B", "C", "A"]}, "timestamp": {"name": "timestamp", "dtype": "datetime64[ns]", "null_count": "0", "null_percentage": 0.0, "unique_count": 2000, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["2025-06-27 09:21:21.223384", "2025-07-07 13:23:31.223384", "2025-07-12 20:15:39.223384", "2025-07-13 02:12:30.223384", "2025-06-30 18:00:22.223384"], "recommended_dtype": "datetime64[ns]", "unique_values": ["2025-06-27 09:21:21.223384", "2025-07-07 13:23:31.223384", "2025-07-12 20:15:39.223384", "2025-07-13 02:12:30.223384", "2025-06-30 18:00:22.223384", "2025-07-13 16:01:32.223384", "2025-07-17 14:38:09.223384", "2025-06-25 19:15:51.223384", "2025-07-18 21:43:04.223384", "2025-07-04 17:55:22.223384"]}}, "label_column": "click", "array_columns": ["context_embedding", "user_embedding", "item_embedding"], "numeric_columns": ["user_id", "item_id", "click", "conversion", "time_spent", "click_position", "session_length", "age", "price", "rating", "noise_1"], "categorical_columns": ["gender", "region", "income_level", "category", "brand", "noise_2", "timestamp"]}}, "test": {"dataset_name": "test", "data_directory": "local_test_data\\small\\test", "num_files": 2, "schema_consistent": true, "estimated_total_rows": 2000, "columns_analysis": {"user_id": {"name": "user_id", "dtype": "int64", "null_count": "0", "null_percentage": 0.0, "unique_count": 50, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [900, 900, 900, 900, 900], "min_value": 900.0, "max_value": 949.0, "mean_value": 924.5, "recommended_dtype": "uint16", "unique_values": [900, 901, 902, 903, 904, 905, 906, 907, 908, 909]}, "item_id": {"name": "item_id", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 432, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [233, 254, 50, 64, 192], "min_value": 0.0, "max_value": 498.0, "mean_value": 245.401, "recommended_dtype": "uint16", "unique_values": [233, 254, 50, 64, 192, 109, 9, 19, 347, 36]}, "click": {"name": "click", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 2, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": true, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 1.0, "mean_value": 0.115, "recommended_dtype": "uint8", "unique_values": [0, 1]}, "conversion": {"name": "conversion", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 2, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": true, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 1.0, "mean_value": 0.021, "recommended_dtype": "uint8", "unique_values": [0, 1]}, "time_spent": {"name": "time_spent", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [971.7606691323381, 744.9072822455485, 852.1065880012904, 1454.6533456415395, 1476.1050957250818], "min_value": 1.34252215827102, "max_value": 3595.821698944432, "mean_value": 1798.1757492005088, "recommended_dtype": "float16", "unique_values": [971.7606691323381, 744.9072822455485, 852.1065880012904, 1454.6533456415395, 1476.1050957250818, 461.6864331396963, 1228.2308732113672, 3589.640505153338, 1528.1542113074968, 565.2972792846142]}, "click_position": {"name": "click_position", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [1.7683232538003386, 8.76389050067587, 12.736110003276384, 9.192978843543315, 3.8399311294584915], "min_value": 1.0002724559300527, "max_value": 19.986930366516955, "mean_value": 10.466474313873807, "recommended_dtype": "float16", "unique_values": [1.7683232538003386, 8.76389050067587, 12.736110003276384, 9.192978843543315, 3.8399311294584915, 5.222417243443876, 6.6700393456321265, 6.2879613851487335, 7.390655708529868, 7.913263938011176]}, "session_length": {"name": "session_length", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [51.58362663183909, 43.15008935470175, 92.89874670513625, 56.992999455243655, 65.44632717360261], "min_value": 1.1787372305540293, "max_value": 99.99106369233837, "mean_value": 49.96853034663685, "recommended_dtype": "float16", "unique_values": [51.58362663183909, 43.15008935470175, 92.89874670513625, 56.992999455243655, 65.44632717360261, 16.471099951702843, 86.29951527676798, 53.967399095096454, 89.82206822875631, 50.98235023558499]}, "context_embedding": {"name": "context_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 32, "sample_values": ["[-0.02840482 -0.11066883  0.1054418  -0.08178722  0.12572054  0.01653364\n  0.12995936 -0.10968433  0.0353818   0.12469824 -0.06137195 -0.03193869\n  0.00662192 -0.00171106  0.01676428  0.08136738  0.02938264 -0.02084616\n -0.15735199 -0.10105762  0.13401366 -0.13131409  0.17888863 -0.06957803\n  0.1556645   0.16541078  0.03425371 -0.06510217  0.05614086  0.17383496\n -0.06074592  0.09528003]", "[-0.02466751  0.0881596   0.09176267  0.00998778 -0.00845167  0.06960388\n -0.13873214 -0.04101323  0.31501705 -0.01003866 -0.02092739 -0.12530787\n -0.00564712  0.03238256 -0.00589823  0.07708013 -0.01618061  0.02587884\n -0.01568575 -0.12097744 -0.09334476 -0.075437   -0.10120719 -0.05677901\n -0.08122238 -0.08925584  0.01839751 -0.01940339  0.17281796  0.05618533\n  0.03952305 -0.08045977]", "[ 0.03641793  0.11562011  0.0461984  -0.04505458  0.03831014  0.16245959\n -0.06590887 -0.04709924  0.01186305 -0.02207246  0.04267433  0.08782133\n -0.09454996 -0.01735246 -0.08341208  0.06348858 -0.03133279 -0.10580592\n  0.04407868 -0.05127807 -0.24411411  0.03350761  0.07014897  0.03572886\n  0.19843096  0.16483182 -0.22035356  0.09934074 -0.03219743 -0.15349325\n  0.26472543  0.0492622 ]", "[-0.00147293 -0.01904919 -0.18895548  0.01610896  0.02083456 -0.18951977\n  0.01219286  0.00405125 -0.11428369 -0.17099643 -0.12571825 -0.0526253\n -0.00692051 -0.07729284  0.16789551  0.21551506  0.06199229  0.07286668\n -0.0134287  -0.0509327  -0.09189854  0.1640373  -0.04114202  0.04204549\n  0.04522489  0.04156913 -0.06534224 -0.09253838  0.10483337  0.01537741\n -0.10358129  0.06086569]", "[-0.03676258  0.00758632  0.07133767  0.10205919 -0.10475086  0.03886967\n -0.01831444 -0.09169175  0.02830682  0.1246174   0.05222297 -0.0527168\n  0.03770756  0.13005745 -0.03521449 -0.10016761 -0.0857481   0.19339898\n  0.00197326 -0.04916411 -0.04559854  0.08789653 -0.00318557  0.08832513\n -0.00203133 -0.20831379 -0.12016662 -0.12898923  0.07565724 -0.1091074\n -0.01161086  0.06110603]"]}, "age": {"name": "age", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 50, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [52.5216560928778, 52.5216560928778, 52.5216560928778, 52.5216560928778, 52.5216560928778], "min_value": 18.163181275108922, "max_value": 78.92645083803208, "mean_value": 47.161392716084, "recommended_dtype": "float16", "unique_values": [52.5216560928778, 68.3383890892744, 52.49107760549736, 69.05191046278823, 24.597192945477758, 31.43248467554923, 24.49593552300722, 64.30818190717491, 60.49992839221143, 69.6404547707243]}, "gender": {"name": "gender", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 3, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["M", "M", "M", "M", "M"], "recommended_dtype": "object", "unique_values": ["M", "F", "O"]}, "region": {"name": "region", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 18, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["region_2", "region_2", "region_2", "region_2", "region_2"], "recommended_dtype": "object", "unique_values": ["region_2", "region_1", "region_13", "region_14", "region_12", "region_4", "region_7", "region_10", "region_0", "region_3"]}, "income_level": {"name": "income_level", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 4, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["very_high", "very_high", "very_high", "very_high", "very_high"], "recommended_dtype": "object", "unique_values": ["very_high", "low", "medium", "high"]}, "user_embedding": {"name": "user_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 64, "sample_values": ["[-0.12482188  0.08200034 -0.10730536 -0.1535593   0.1307356  -0.28464111\n -0.08928005  0.07175054  0.1113516  -0.03035187  0.21393219  0.06377673\n -0.12266415  0.10094779  0.20692071  0.18645919  0.02685139 -0.01933193\n -0.0153062   0.05126482  0.05120762 -0.17661579  0.14606359 -0.03413625\n  0.11817485  0.12794344  0.07259781 -0.10529035 -0.08571142  0.00836527\n -0.02677063 -0.04442736  0.0261318  -0.00743758  0.0382381  -0.07287749\n -0.13470821  0.12994496 -0.01686977 -0.06726514 -0.02144397 -0.0800927\n  0.00983186 -0.11382597 -0.14206558 -0.0309727  -0.0829176  -0.10980597\n -0.09396958  0.04529799 -0.00370006 -0.03071478  0.17085275 -0.12996909\n  0.00794752  0.00208353 -0.00974845  0.1044451   0.03280808 -0.04128173\n -0.09174432 -0.07941877 -0.12912581 -0.06333043]", "[-0.12482188  0.08200034 -0.10730536 -0.1535593   0.1307356  -0.28464111\n -0.08928005  0.07175054  0.1113516  -0.03035187  0.21393219  0.06377673\n -0.12266415  0.10094779  0.20692071  0.18645919  0.02685139 -0.01933193\n -0.0153062   0.05126482  0.05120762 -0.17661579  0.14606359 -0.03413625\n  0.11817485  0.12794344  0.07259781 -0.10529035 -0.08571142  0.00836527\n -0.02677063 -0.04442736  0.0261318  -0.00743758  0.0382381  -0.07287749\n -0.13470821  0.12994496 -0.01686977 -0.06726514 -0.02144397 -0.0800927\n  0.00983186 -0.11382597 -0.14206558 -0.0309727  -0.0829176  -0.10980597\n -0.09396958  0.04529799 -0.00370006 -0.03071478  0.17085275 -0.12996909\n  0.00794752  0.00208353 -0.00974845  0.1044451   0.03280808 -0.04128173\n -0.09174432 -0.07941877 -0.12912581 -0.06333043]", "[-0.12482188  0.08200034 -0.10730536 -0.1535593   0.1307356  -0.28464111\n -0.08928005  0.07175054  0.1113516  -0.03035187  0.21393219  0.06377673\n -0.12266415  0.10094779  0.20692071  0.18645919  0.02685139 -0.01933193\n -0.0153062   0.05126482  0.05120762 -0.17661579  0.14606359 -0.03413625\n  0.11817485  0.12794344  0.07259781 -0.10529035 -0.08571142  0.00836527\n -0.02677063 -0.04442736  0.0261318  -0.00743758  0.0382381  -0.07287749\n -0.13470821  0.12994496 -0.01686977 -0.06726514 -0.02144397 -0.0800927\n  0.00983186 -0.11382597 -0.14206558 -0.0309727  -0.0829176  -0.10980597\n -0.09396958  0.04529799 -0.00370006 -0.03071478  0.17085275 -0.12996909\n  0.00794752  0.00208353 -0.00974845  0.1044451   0.03280808 -0.04128173\n -0.09174432 -0.07941877 -0.12912581 -0.06333043]", "[-0.12482188  0.08200034 -0.10730536 -0.1535593   0.1307356  -0.28464111\n -0.08928005  0.07175054  0.1113516  -0.03035187  0.21393219  0.06377673\n -0.12266415  0.10094779  0.20692071  0.18645919  0.02685139 -0.01933193\n -0.0153062   0.05126482  0.05120762 -0.17661579  0.14606359 -0.03413625\n  0.11817485  0.12794344  0.07259781 -0.10529035 -0.08571142  0.00836527\n -0.02677063 -0.04442736  0.0261318  -0.00743758  0.0382381  -0.07287749\n -0.13470821  0.12994496 -0.01686977 -0.06726514 -0.02144397 -0.0800927\n  0.00983186 -0.11382597 -0.14206558 -0.0309727  -0.0829176  -0.10980597\n -0.09396958  0.04529799 -0.00370006 -0.03071478  0.17085275 -0.12996909\n  0.00794752  0.00208353 -0.00974845  0.1044451   0.03280808 -0.04128173\n -0.09174432 -0.07941877 -0.12912581 -0.06333043]", "[-0.12482188  0.08200034 -0.10730536 -0.1535593   0.1307356  -0.28464111\n -0.08928005  0.07175054  0.1113516  -0.03035187  0.21393219  0.06377673\n -0.12266415  0.10094779  0.20692071  0.18645919  0.02685139 -0.01933193\n -0.0153062   0.05126482  0.05120762 -0.17661579  0.14606359 -0.03413625\n  0.11817485  0.12794344  0.07259781 -0.10529035 -0.08571142  0.00836527\n -0.02677063 -0.04442736  0.0261318  -0.00743758  0.0382381  -0.07287749\n -0.13470821  0.12994496 -0.01686977 -0.06726514 -0.02144397 -0.0800927\n  0.00983186 -0.11382597 -0.14206558 -0.0309727  -0.0829176  -0.10980597\n -0.09396958  0.04529799 -0.00370006 -0.03071478  0.17085275 -0.12996909\n  0.00794752  0.00208353 -0.00974845  0.1044451   0.03280808 -0.04128173\n -0.09174432 -0.07941877 -0.12912581 -0.06333043]"]}, "category": {"name": "category", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 10, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["cat_1", "cat_2", "cat_5", "cat_2", "cat_7"], "recommended_dtype": "object", "unique_values": ["cat_1", "cat_2", "cat_5", "cat_7", "cat_4", "cat_9", "cat_6", "cat_3", "cat_0", "cat_8"]}, "price": {"name": "price", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 432, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [421.9185467916298, 801.6720451888857, 638.7458502508905, 929.0635767556922, 410.4030859540801], "min_value": 1.756018129078533, "max_value": 999.2101141834955, "mean_value": 488.40685325263047, "recommended_dtype": "float16", "unique_values": [421.9185467916298, 801.6720451888857, 638.7458502508905, 929.0635767556922, 410.4030859540801, 421.5184211109461, 103.35991449814023, 560.3205934039432, 699.7265882863212, 77.19297638965962]}, "brand": {"name": "brand", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 50, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["brand_15", "brand_3", "brand_5", "brand_0", "brand_36"], "recommended_dtype": "object", "unique_values": ["brand_15", "brand_3", "brand_5", "brand_0", "brand_36", "brand_24", "brand_27", "brand_17", "brand_39", "brand_8"]}, "rating": {"name": "rating", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 432, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [4.052201300950709, 1.3714724097534172, 4.583648486300001, 2.8757619426726118, 2.4320893015057052], "min_value": 1.0146035123110448, "max_value": 4.9925715069283445, "mean_value": 2.9701693849079933, "recommended_dtype": "float16", "unique_values": [4.052201300950709, 1.3714724097534172, 4.583648486300001, 2.8757619426726118, 2.4320893015057052, 2.0535223265433156, 4.487703357359559, 2.0673011575895996, 3.918552730184377, 3.179013191497646]}, "item_embedding": {"name": "item_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 64, "sample_values": ["[ 0.10319807  0.04111579 -0.09529525  0.06106396  0.17353523 -0.09167\n -0.04781674  0.12132407 -0.10089863 -0.05182265 -0.08766992  0.09216876\n  0.13159797  0.02951298  0.15556884  0.09546477  0.08002535  0.16295626\n -0.11754041  0.11867367 -0.03883272 -0.06165788  0.17415024  0.00960007\n  0.00335362  0.00456429  0.07212755  0.03712555 -0.02802835 -0.21186879\n  0.00274056 -0.06414785 -0.06582633 -0.20689879 -0.06298728  0.02928284\n  0.04901256 -0.13860793 -0.0260576  -0.08154246 -0.10152414  0.03770139\n -0.00074729 -0.14277568  0.00571953 -0.09864573 -0.23719442  0.08408573\n  0.05233514  0.01526952 -0.14158471  0.12308598 -0.06275223 -0.02638977\n -0.06917212  0.16621198  0.09139369  0.09854205  0.20013474  0.14946015\n  0.1228209   0.01624895  0.08993718 -0.00778992]", "[ 0.11349387 -0.03326521  0.00872385 -0.07522611  0.00601079  0.06663276\n -0.11254823 -0.11093083 -0.08283516  0.06703441  0.0979148  -0.03582023\n  0.14415011  0.04165061  0.29796543  0.03140027 -0.02079722  0.0030618\n -0.00920819  0.0410529  -0.08183645 -0.0800349  -0.11767595  0.20208804\n -0.07044416  0.0357256  -0.04393561 -0.03905521  0.04609879 -0.0242441\n  0.1136846  -0.0332026   0.01282017 -0.14998121 -0.14780565 -0.1312253\n  0.09221261 -0.11018812  0.02618565  0.04565092  0.01110166 -0.08910606\n -0.12312364  0.10954003 -0.01373916  0.12811575 -0.02711816  0.03283298\n  0.13260045  0.05353409 -0.04237419  0.01508043 -0.00239254  0.02706992\n -0.11839416  0.16430577 -0.08377828  0.02851742  0.06802909  0.00286733\n -0.12458077  0.17913047 -0.2473248   0.12025928]", "[-8.69908180e-02 -4.07759741e-02  6.64131657e-02 -5.79904172e-02\n  9.28353431e-03 -1.35043025e-01  7.98339174e-05  1.84906621e-02\n -4.90266991e-02 -2.35236705e-01 -8.64531591e-02  2.59715069e-01\n  5.92190127e-02 -5.68437087e-02 -5.02141404e-02 -6.16417861e-02\n  5.85095448e-02  6.02248478e-02 -6.60056856e-02 -1.14041286e-01\n -8.73852496e-02 -1.67545200e-01 -4.87227603e-02 -4.25718689e-02\n  4.58601098e-02 -6.03882904e-02 -1.41716830e-01  1.19431363e-02\n  9.46726874e-02  1.13863410e-01  1.30394107e-01  2.00893274e-02\n -1.70958788e-01 -3.41476282e-02  9.75156137e-02  3.16827218e-02\n  1.03666214e-01 -1.87491363e-02  4.73205651e-02 -4.26041776e-03\n -7.74194869e-03  6.66344484e-03 -5.94776937e-02  7.77206927e-02\n -2.81047129e-02 -5.94738194e-02  8.22512480e-02 -9.95003976e-02\n  5.88829522e-02  3.46978537e-02 -1.57372289e-02  4.36739424e-02\n  1.01153883e-01  1.18136317e-01 -1.89247823e-01 -1.37845068e-01\n -4.55530388e-03  3.99498605e-02 -8.04750353e-03 -1.72170301e-02\n -3.37400238e-02 -1.02498653e-01 -8.80136551e-02  2.61728283e-02]", "[ 0.03122033  0.20017319  0.04475739  0.04778583  0.01049318  0.0136227\n  0.08852513 -0.13039931 -0.12615637 -0.00875329 -0.08775637  0.11292389\n  0.12590335 -0.12783016  0.13800026  0.14247001  0.14217975  0.04184593\n -0.03125399 -0.06782694  0.18274176  0.1981812   0.0065029   0.08034417\n -0.09298506  0.01696978 -0.08720127 -0.15416114 -0.12896051 -0.05589412\n -0.04319896  0.06445106  0.03156673 -0.0620777   0.28868725 -0.02707499\n -0.0549003  -0.02958686 -0.03714964  0.21045784 -0.00214376 -0.08925257\n  0.07359467 -0.13690177 -0.15464166  0.0025388   0.06895145 -0.00500477\n  0.08754227 -0.02746196  0.2599217  -0.13182637 -0.00216918 -0.11970057\n  0.04232199 -0.07339409 -0.00052522 -0.16725853  0.08798839 -0.3375372\n  0.10165633  0.05611927 -0.10138422  0.01588596]", "[ 0.01778604 -0.05331232  0.00494471  0.05691016 -0.08773776  0.14777683\n -0.09734257 -0.01817227  0.12042375 -0.0996814   0.02240296 -0.02920226\n  0.03831359 -0.08331339 -0.04874421  0.09318727  0.00306597  0.06599457\n -0.17146001  0.05727636  0.0163944   0.22223515 -0.01945515 -0.11579377\n -0.11355184 -0.05114372  0.17940687 -0.134455    0.05084694  0.02944125\n -0.13540598 -0.19537035  0.03957623  0.11981306  0.0653013  -0.12598619\n -0.01048518 -0.06155602 -0.1164245   0.03190374  0.02511588 -0.0801201\n  0.13309583 -0.10163024  0.18434654 -0.02796243  0.02148032 -0.02561533\n  0.04118613 -0.09831852 -0.09189996 -0.01012334 -0.0217972  -0.11609353\n  0.16164714 -0.02642993 -0.00278784  0.16624316 -0.03176392 -0.12566197\n -0.07978704  0.07975568  0.12623255  0.15253052]"]}, "noise_1": {"name": "noise_1", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [1.8094652497018477, 0.9385224792339782, -0.3650913742576487, -0.5358001957354425, 0.840131660979747], "min_value": -3.3651265176676874, "max_value": 3.1218718849566383, "mean_value": 0.016548587693708646, "recommended_dtype": "float16", "unique_values": [1.8094652497018477, 0.9385224792339782, -0.3650913742576487, -0.5358001957354425, 0.840131660979747, -0.5718799259817761, -0.5824309855613086, -0.6963975676262545, -0.46588443152726494, -0.4992381099247684]}, "noise_2": {"name": "noise_2", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 3, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["B", "C", "C", "C", "B"], "recommended_dtype": "object", "unique_values": ["B", "C", "A"]}, "timestamp": {"name": "timestamp", "dtype": "datetime64[ns]", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["2025-06-26 07:48:39.223384", "2025-07-01 15:41:10.223384", "2025-06-28 17:57:06.223384", "2025-07-05 19:01:22.223384", "2025-07-16 19:18:14.223384"], "recommended_dtype": "datetime64[ns]", "unique_values": ["2025-06-26 07:48:39.223384", "2025-07-01 15:41:10.223384", "2025-06-28 17:57:06.223384", "2025-07-05 19:01:22.223384", "2025-07-16 19:18:14.223384", "2025-06-25 11:24:32.223384", "2025-06-29 21:19:43.223384", "2025-07-04 15:29:17.223384", "2025-06-26 21:46:55.223384", "2025-07-12 01:30:25.223384"]}}, "label_column": "click", "array_columns": ["context_embedding", "user_embedding", "item_embedding"], "numeric_columns": ["user_id", "item_id", "click", "conversion", "time_spent", "click_position", "session_length", "age", "price", "rating", "noise_1"], "categorical_columns": ["gender", "region", "income_level", "category", "brand", "noise_2", "timestamp"], "sample_file_analysis": {"file_path": "local_test_data\\small\\test\\data_chunk_000.parquet", "total_rows": 1000, "total_columns": 21, "columns": {"user_id": {"name": "user_id", "dtype": "int64", "null_count": "0", "null_percentage": 0.0, "unique_count": 50, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [900, 900, 900, 900, 900], "min_value": 900.0, "max_value": 949.0, "mean_value": 924.5, "recommended_dtype": "uint16", "unique_values": [900, 901, 902, 903, 904, 905, 906, 907, 908, 909]}, "item_id": {"name": "item_id", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 432, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [233, 254, 50, 64, 192], "min_value": 0.0, "max_value": 498.0, "mean_value": 245.401, "recommended_dtype": "uint16", "unique_values": [233, 254, 50, 64, 192, 109, 9, 19, 347, 36]}, "click": {"name": "click", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 2, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": true, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 1.0, "mean_value": 0.115, "recommended_dtype": "uint8", "unique_values": [0, 1]}, "conversion": {"name": "conversion", "dtype": "int32", "null_count": "0", "null_percentage": 0.0, "unique_count": 2, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": true, "array_length": null, "sample_values": [0, 0, 0, 0, 0], "min_value": 0.0, "max_value": 1.0, "mean_value": 0.021, "recommended_dtype": "uint8", "unique_values": [0, 1]}, "time_spent": {"name": "time_spent", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [971.7606691323381, 744.9072822455485, 852.1065880012904, 1454.6533456415395, 1476.1050957250818], "min_value": 1.34252215827102, "max_value": 3595.821698944432, "mean_value": 1798.1757492005088, "recommended_dtype": "float16", "unique_values": [971.7606691323381, 744.9072822455485, 852.1065880012904, 1454.6533456415395, 1476.1050957250818, 461.6864331396963, 1228.2308732113672, 3589.640505153338, 1528.1542113074968, 565.2972792846142]}, "click_position": {"name": "click_position", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [1.7683232538003386, 8.76389050067587, 12.736110003276384, 9.192978843543315, 3.8399311294584915], "min_value": 1.0002724559300527, "max_value": 19.986930366516955, "mean_value": 10.466474313873807, "recommended_dtype": "float16", "unique_values": [1.7683232538003386, 8.76389050067587, 12.736110003276384, 9.192978843543315, 3.8399311294584915, 5.222417243443876, 6.6700393456321265, 6.2879613851487335, 7.390655708529868, 7.913263938011176]}, "session_length": {"name": "session_length", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [51.58362663183909, 43.15008935470175, 92.89874670513625, 56.992999455243655, 65.44632717360261], "min_value": 1.1787372305540293, "max_value": 99.99106369233837, "mean_value": 49.96853034663685, "recommended_dtype": "float16", "unique_values": [51.58362663183909, 43.15008935470175, 92.89874670513625, 56.992999455243655, 65.44632717360261, 16.471099951702843, 86.29951527676798, 53.967399095096454, 89.82206822875631, 50.98235023558499]}, "context_embedding": {"name": "context_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 32, "sample_values": ["[-0.02840482 -0.11066883  0.1054418  -0.08178722  0.12572054  0.01653364\n  0.12995936 -0.10968433  0.0353818   0.12469824 -0.06137195 -0.03193869\n  0.00662192 -0.00171106  0.01676428  0.08136738  0.02938264 -0.02084616\n -0.15735199 -0.10105762  0.13401366 -0.13131409  0.17888863 -0.06957803\n  0.1556645   0.16541078  0.03425371 -0.06510217  0.05614086  0.17383496\n -0.06074592  0.09528003]", "[-0.02466751  0.0881596   0.09176267  0.00998778 -0.00845167  0.06960388\n -0.13873214 -0.04101323  0.31501705 -0.01003866 -0.02092739 -0.12530787\n -0.00564712  0.03238256 -0.00589823  0.07708013 -0.01618061  0.02587884\n -0.01568575 -0.12097744 -0.09334476 -0.075437   -0.10120719 -0.05677901\n -0.08122238 -0.08925584  0.01839751 -0.01940339  0.17281796  0.05618533\n  0.03952305 -0.08045977]", "[ 0.03641793  0.11562011  0.0461984  -0.04505458  0.03831014  0.16245959\n -0.06590887 -0.04709924  0.01186305 -0.02207246  0.04267433  0.08782133\n -0.09454996 -0.01735246 -0.08341208  0.06348858 -0.03133279 -0.10580592\n  0.04407868 -0.05127807 -0.24411411  0.03350761  0.07014897  0.03572886\n  0.19843096  0.16483182 -0.22035356  0.09934074 -0.03219743 -0.15349325\n  0.26472543  0.0492622 ]", "[-0.00147293 -0.01904919 -0.18895548  0.01610896  0.02083456 -0.18951977\n  0.01219286  0.00405125 -0.11428369 -0.17099643 -0.12571825 -0.0526253\n -0.00692051 -0.07729284  0.16789551  0.21551506  0.06199229  0.07286668\n -0.0134287  -0.0509327  -0.09189854  0.1640373  -0.04114202  0.04204549\n  0.04522489  0.04156913 -0.06534224 -0.09253838  0.10483337  0.01537741\n -0.10358129  0.06086569]", "[-0.03676258  0.00758632  0.07133767  0.10205919 -0.10475086  0.03886967\n -0.01831444 -0.09169175  0.02830682  0.1246174   0.05222297 -0.0527168\n  0.03770756  0.13005745 -0.03521449 -0.10016761 -0.0857481   0.19339898\n  0.00197326 -0.04916411 -0.04559854  0.08789653 -0.00318557  0.08832513\n -0.00203133 -0.20831379 -0.12016662 -0.12898923  0.07565724 -0.1091074\n -0.01161086  0.06110603]"]}, "age": {"name": "age", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 50, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [52.5216560928778, 52.5216560928778, 52.5216560928778, 52.5216560928778, 52.5216560928778], "min_value": 18.163181275108922, "max_value": 78.92645083803208, "mean_value": 47.161392716084, "recommended_dtype": "float16", "unique_values": [52.5216560928778, 68.3383890892744, 52.49107760549736, 69.05191046278823, 24.597192945477758, 31.43248467554923, 24.49593552300722, 64.30818190717491, 60.49992839221143, 69.6404547707243]}, "gender": {"name": "gender", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 3, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["M", "M", "M", "M", "M"], "recommended_dtype": "object", "unique_values": ["M", "F", "O"]}, "region": {"name": "region", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 18, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["region_2", "region_2", "region_2", "region_2", "region_2"], "recommended_dtype": "object", "unique_values": ["region_2", "region_1", "region_13", "region_14", "region_12", "region_4", "region_7", "region_10", "region_0", "region_3"]}, "income_level": {"name": "income_level", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 4, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["very_high", "very_high", "very_high", "very_high", "very_high"], "recommended_dtype": "object", "unique_values": ["very_high", "low", "medium", "high"]}, "user_embedding": {"name": "user_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 64, "sample_values": ["[-0.12482188  0.08200034 -0.10730536 -0.1535593   0.1307356  -0.28464111\n -0.08928005  0.07175054  0.1113516  -0.03035187  0.21393219  0.06377673\n -0.12266415  0.10094779  0.20692071  0.18645919  0.02685139 -0.01933193\n -0.0153062   0.05126482  0.05120762 -0.17661579  0.14606359 -0.03413625\n  0.11817485  0.12794344  0.07259781 -0.10529035 -0.08571142  0.00836527\n -0.02677063 -0.04442736  0.0261318  -0.00743758  0.0382381  -0.07287749\n -0.13470821  0.12994496 -0.01686977 -0.06726514 -0.02144397 -0.0800927\n  0.00983186 -0.11382597 -0.14206558 -0.0309727  -0.0829176  -0.10980597\n -0.09396958  0.04529799 -0.00370006 -0.03071478  0.17085275 -0.12996909\n  0.00794752  0.00208353 -0.00974845  0.1044451   0.03280808 -0.04128173\n -0.09174432 -0.07941877 -0.12912581 -0.06333043]", "[-0.12482188  0.08200034 -0.10730536 -0.1535593   0.1307356  -0.28464111\n -0.08928005  0.07175054  0.1113516  -0.03035187  0.21393219  0.06377673\n -0.12266415  0.10094779  0.20692071  0.18645919  0.02685139 -0.01933193\n -0.0153062   0.05126482  0.05120762 -0.17661579  0.14606359 -0.03413625\n  0.11817485  0.12794344  0.07259781 -0.10529035 -0.08571142  0.00836527\n -0.02677063 -0.04442736  0.0261318  -0.00743758  0.0382381  -0.07287749\n -0.13470821  0.12994496 -0.01686977 -0.06726514 -0.02144397 -0.0800927\n  0.00983186 -0.11382597 -0.14206558 -0.0309727  -0.0829176  -0.10980597\n -0.09396958  0.04529799 -0.00370006 -0.03071478  0.17085275 -0.12996909\n  0.00794752  0.00208353 -0.00974845  0.1044451   0.03280808 -0.04128173\n -0.09174432 -0.07941877 -0.12912581 -0.06333043]", "[-0.12482188  0.08200034 -0.10730536 -0.1535593   0.1307356  -0.28464111\n -0.08928005  0.07175054  0.1113516  -0.03035187  0.21393219  0.06377673\n -0.12266415  0.10094779  0.20692071  0.18645919  0.02685139 -0.01933193\n -0.0153062   0.05126482  0.05120762 -0.17661579  0.14606359 -0.03413625\n  0.11817485  0.12794344  0.07259781 -0.10529035 -0.08571142  0.00836527\n -0.02677063 -0.04442736  0.0261318  -0.00743758  0.0382381  -0.07287749\n -0.13470821  0.12994496 -0.01686977 -0.06726514 -0.02144397 -0.0800927\n  0.00983186 -0.11382597 -0.14206558 -0.0309727  -0.0829176  -0.10980597\n -0.09396958  0.04529799 -0.00370006 -0.03071478  0.17085275 -0.12996909\n  0.00794752  0.00208353 -0.00974845  0.1044451   0.03280808 -0.04128173\n -0.09174432 -0.07941877 -0.12912581 -0.06333043]", "[-0.12482188  0.08200034 -0.10730536 -0.1535593   0.1307356  -0.28464111\n -0.08928005  0.07175054  0.1113516  -0.03035187  0.21393219  0.06377673\n -0.12266415  0.10094779  0.20692071  0.18645919  0.02685139 -0.01933193\n -0.0153062   0.05126482  0.05120762 -0.17661579  0.14606359 -0.03413625\n  0.11817485  0.12794344  0.07259781 -0.10529035 -0.08571142  0.00836527\n -0.02677063 -0.04442736  0.0261318  -0.00743758  0.0382381  -0.07287749\n -0.13470821  0.12994496 -0.01686977 -0.06726514 -0.02144397 -0.0800927\n  0.00983186 -0.11382597 -0.14206558 -0.0309727  -0.0829176  -0.10980597\n -0.09396958  0.04529799 -0.00370006 -0.03071478  0.17085275 -0.12996909\n  0.00794752  0.00208353 -0.00974845  0.1044451   0.03280808 -0.04128173\n -0.09174432 -0.07941877 -0.12912581 -0.06333043]", "[-0.12482188  0.08200034 -0.10730536 -0.1535593   0.1307356  -0.28464111\n -0.08928005  0.07175054  0.1113516  -0.03035187  0.21393219  0.06377673\n -0.12266415  0.10094779  0.20692071  0.18645919  0.02685139 -0.01933193\n -0.0153062   0.05126482  0.05120762 -0.17661579  0.14606359 -0.03413625\n  0.11817485  0.12794344  0.07259781 -0.10529035 -0.08571142  0.00836527\n -0.02677063 -0.04442736  0.0261318  -0.00743758  0.0382381  -0.07287749\n -0.13470821  0.12994496 -0.01686977 -0.06726514 -0.02144397 -0.0800927\n  0.00983186 -0.11382597 -0.14206558 -0.0309727  -0.0829176  -0.10980597\n -0.09396958  0.04529799 -0.00370006 -0.03071478  0.17085275 -0.12996909\n  0.00794752  0.00208353 -0.00974845  0.1044451   0.03280808 -0.04128173\n -0.09174432 -0.07941877 -0.12912581 -0.06333043]"]}, "category": {"name": "category", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 10, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["cat_1", "cat_2", "cat_5", "cat_2", "cat_7"], "recommended_dtype": "object", "unique_values": ["cat_1", "cat_2", "cat_5", "cat_7", "cat_4", "cat_9", "cat_6", "cat_3", "cat_0", "cat_8"]}, "price": {"name": "price", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 432, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [421.9185467916298, 801.6720451888857, 638.7458502508905, 929.0635767556922, 410.4030859540801], "min_value": 1.756018129078533, "max_value": 999.2101141834955, "mean_value": 488.40685325263047, "recommended_dtype": "float16", "unique_values": [421.9185467916298, 801.6720451888857, 638.7458502508905, 929.0635767556922, 410.4030859540801, 421.5184211109461, 103.35991449814023, 560.3205934039432, 699.7265882863212, 77.19297638965962]}, "brand": {"name": "brand", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 50, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["brand_15", "brand_3", "brand_5", "brand_0", "brand_36"], "recommended_dtype": "object", "unique_values": ["brand_15", "brand_3", "brand_5", "brand_0", "brand_36", "brand_24", "brand_27", "brand_17", "brand_39", "brand_8"]}, "rating": {"name": "rating", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 432, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [4.052201300950709, 1.3714724097534172, 4.583648486300001, 2.8757619426726118, 2.4320893015057052], "min_value": 1.0146035123110448, "max_value": 4.9925715069283445, "mean_value": 2.9701693849079933, "recommended_dtype": "float16", "unique_values": [4.052201300950709, 1.3714724097534172, 4.583648486300001, 2.8757619426726118, 2.4320893015057052, 2.0535223265433156, 4.487703357359559, 2.0673011575895996, 3.918552730184377, 3.179013191497646]}, "item_embedding": {"name": "item_embedding", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": true, "is_numeric": false, "is_categorical": false, "is_binary": false, "array_length": 64, "sample_values": ["[ 0.10319807  0.04111579 -0.09529525  0.06106396  0.17353523 -0.09167\n -0.04781674  0.12132407 -0.10089863 -0.05182265 -0.08766992  0.09216876\n  0.13159797  0.02951298  0.15556884  0.09546477  0.08002535  0.16295626\n -0.11754041  0.11867367 -0.03883272 -0.06165788  0.17415024  0.00960007\n  0.00335362  0.00456429  0.07212755  0.03712555 -0.02802835 -0.21186879\n  0.00274056 -0.06414785 -0.06582633 -0.20689879 -0.06298728  0.02928284\n  0.04901256 -0.13860793 -0.0260576  -0.08154246 -0.10152414  0.03770139\n -0.00074729 -0.14277568  0.00571953 -0.09864573 -0.23719442  0.08408573\n  0.05233514  0.01526952 -0.14158471  0.12308598 -0.06275223 -0.02638977\n -0.06917212  0.16621198  0.09139369  0.09854205  0.20013474  0.14946015\n  0.1228209   0.01624895  0.08993718 -0.00778992]", "[ 0.11349387 -0.03326521  0.00872385 -0.07522611  0.00601079  0.06663276\n -0.11254823 -0.11093083 -0.08283516  0.06703441  0.0979148  -0.03582023\n  0.14415011  0.04165061  0.29796543  0.03140027 -0.02079722  0.0030618\n -0.00920819  0.0410529  -0.08183645 -0.0800349  -0.11767595  0.20208804\n -0.07044416  0.0357256  -0.04393561 -0.03905521  0.04609879 -0.0242441\n  0.1136846  -0.0332026   0.01282017 -0.14998121 -0.14780565 -0.1312253\n  0.09221261 -0.11018812  0.02618565  0.04565092  0.01110166 -0.08910606\n -0.12312364  0.10954003 -0.01373916  0.12811575 -0.02711816  0.03283298\n  0.13260045  0.05353409 -0.04237419  0.01508043 -0.00239254  0.02706992\n -0.11839416  0.16430577 -0.08377828  0.02851742  0.06802909  0.00286733\n -0.12458077  0.17913047 -0.2473248   0.12025928]", "[-8.69908180e-02 -4.07759741e-02  6.64131657e-02 -5.79904172e-02\n  9.28353431e-03 -1.35043025e-01  7.98339174e-05  1.84906621e-02\n -4.90266991e-02 -2.35236705e-01 -8.64531591e-02  2.59715069e-01\n  5.92190127e-02 -5.68437087e-02 -5.02141404e-02 -6.16417861e-02\n  5.85095448e-02  6.02248478e-02 -6.60056856e-02 -1.14041286e-01\n -8.73852496e-02 -1.67545200e-01 -4.87227603e-02 -4.25718689e-02\n  4.58601098e-02 -6.03882904e-02 -1.41716830e-01  1.19431363e-02\n  9.46726874e-02  1.13863410e-01  1.30394107e-01  2.00893274e-02\n -1.70958788e-01 -3.41476282e-02  9.75156137e-02  3.16827218e-02\n  1.03666214e-01 -1.87491363e-02  4.73205651e-02 -4.26041776e-03\n -7.74194869e-03  6.66344484e-03 -5.94776937e-02  7.77206927e-02\n -2.81047129e-02 -5.94738194e-02  8.22512480e-02 -9.95003976e-02\n  5.88829522e-02  3.46978537e-02 -1.57372289e-02  4.36739424e-02\n  1.01153883e-01  1.18136317e-01 -1.89247823e-01 -1.37845068e-01\n -4.55530388e-03  3.99498605e-02 -8.04750353e-03 -1.72170301e-02\n -3.37400238e-02 -1.02498653e-01 -8.80136551e-02  2.61728283e-02]", "[ 0.03122033  0.20017319  0.04475739  0.04778583  0.01049318  0.0136227\n  0.08852513 -0.13039931 -0.12615637 -0.00875329 -0.08775637  0.11292389\n  0.12590335 -0.12783016  0.13800026  0.14247001  0.14217975  0.04184593\n -0.03125399 -0.06782694  0.18274176  0.1981812   0.0065029   0.08034417\n -0.09298506  0.01696978 -0.08720127 -0.15416114 -0.12896051 -0.05589412\n -0.04319896  0.06445106  0.03156673 -0.0620777   0.28868725 -0.02707499\n -0.0549003  -0.02958686 -0.03714964  0.21045784 -0.00214376 -0.08925257\n  0.07359467 -0.13690177 -0.15464166  0.0025388   0.06895145 -0.00500477\n  0.08754227 -0.02746196  0.2599217  -0.13182637 -0.00216918 -0.11970057\n  0.04232199 -0.07339409 -0.00052522 -0.16725853  0.08798839 -0.3375372\n  0.10165633  0.05611927 -0.10138422  0.01588596]", "[ 0.01778604 -0.05331232  0.00494471  0.05691016 -0.08773776  0.14777683\n -0.09734257 -0.01817227  0.12042375 -0.0996814   0.02240296 -0.02920226\n  0.03831359 -0.08331339 -0.04874421  0.09318727  0.00306597  0.06599457\n -0.17146001  0.05727636  0.0163944   0.22223515 -0.01945515 -0.11579377\n -0.11355184 -0.05114372  0.17940687 -0.134455    0.05084694  0.02944125\n -0.13540598 -0.19537035  0.03957623  0.11981306  0.0653013  -0.12598619\n -0.01048518 -0.06155602 -0.1164245   0.03190374  0.02511588 -0.0801201\n  0.13309583 -0.10163024  0.18434654 -0.02796243  0.02148032 -0.02561533\n  0.04118613 -0.09831852 -0.09189996 -0.01012334 -0.0217972  -0.11609353\n  0.16164714 -0.02642993 -0.00278784  0.16624316 -0.03176392 -0.12566197\n -0.07978704  0.07975568  0.12623255  0.15253052]"]}, "noise_1": {"name": "noise_1", "dtype": "float64", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": false, "is_numeric": true, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": [1.8094652497018477, 0.9385224792339782, -0.3650913742576487, -0.5358001957354425, 0.840131660979747], "min_value": -3.3651265176676874, "max_value": 3.1218718849566383, "mean_value": 0.016548587693708646, "recommended_dtype": "float16", "unique_values": [1.8094652497018477, 0.9385224792339782, -0.3650913742576487, -0.5358001957354425, 0.840131660979747, -0.5718799259817761, -0.5824309855613086, -0.6963975676262545, -0.46588443152726494, -0.4992381099247684]}, "noise_2": {"name": "noise_2", "dtype": "object", "null_count": "0", "null_percentage": 0.0, "unique_count": 3, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["B", "C", "C", "C", "B"], "recommended_dtype": "object", "unique_values": ["B", "C", "A"]}, "timestamp": {"name": "timestamp", "dtype": "datetime64[ns]", "null_count": "0", "null_percentage": 0.0, "unique_count": 1000, "is_array": false, "is_numeric": false, "is_categorical": true, "is_binary": false, "array_length": null, "sample_values": ["2025-06-26 07:48:39.223384", "2025-07-01 15:41:10.223384", "2025-06-28 17:57:06.223384", "2025-07-05 19:01:22.223384", "2025-07-16 19:18:14.223384"], "recommended_dtype": "datetime64[ns]", "unique_values": ["2025-06-26 07:48:39.223384", "2025-07-01 15:41:10.223384", "2025-06-28 17:57:06.223384", "2025-07-05 19:01:22.223384", "2025-07-16 19:18:14.223384", "2025-06-25 11:24:32.223384", "2025-06-29 21:19:43.223384", "2025-07-04 15:29:17.223384", "2025-06-26 21:46:55.223384", "2025-07-12 01:30:25.223384"]}}, "label_column": "click", "array_columns": ["context_embedding", "user_embedding", "item_embedding"], "numeric_columns": ["user_id", "item_id", "click", "conversion", "time_spent", "click_position", "session_length", "age", "price", "rating", "noise_1"], "categorical_columns": ["gender", "region", "income_level", "category", "brand", "noise_2", "timestamp"]}}}, "summary": {"total_datasets": 3, "available_datasets": ["train", "validation", "test"], "total_columns": 21, "detected_label_column": "click", "array_columns_count": 3, "numeric_columns_count": 11, "categorical_columns_count": 7, "array_columns": ["context_embedding", "user_embedding", "item_embedding"], "numeric_columns": ["user_id", "item_id", "click", "conversion", "time_spent", "click_position", "session_length", "age", "price", "rating", "noise_1"], "categorical_columns": ["gender", "region", "income_level", "category", "brand", "noise_2", "timestamp"]}, "recommendations": ["Found 3 array columns. These will be flattened during preprocessing."]}