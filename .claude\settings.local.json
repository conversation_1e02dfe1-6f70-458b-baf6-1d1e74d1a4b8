{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(C:UsersmathtDownloadswin_projectrecommendationrecommendation_venvScriptspython.exe test_multiple_configs.py)", "<PERSON><PERSON>(python test:*)", "Bash(..\\recommendation_venv\\Scripts\\python.exe test_multiple_configs.py)", "Bash(..recommendation_venvScriptspython.exe test_multiple_configs.py)", "<PERSON><PERSON>(mv:*)", "Bash(..recommendation_venvScriptspython.exe quick_test.py)", "Bash(\"..\\recommendation_venv\\Scripts\\python.exe\" quick_test.py)", "Bash(\"..\\recommendation_venv\\Scripts\\python.exe\" teststest_compliance.py --quick)", "Bash(\"..\\recommendation_venv\\Scripts\\python.exe\" \"tests\\test_compliance.py\" --quick)", "Bash(\"..\\recommendation_venv\\Scripts\\python.exe\":*)", "<PERSON><PERSON>(dir:*)", "Bash(C:UsersmathtDownloadswin_projectrecommendationrecommendation_venvScriptspython.exe -u src/train_loss_optimized.py --model_type dcnv1 --epochs 1 --pos_weight_strategy sqrt_balanced --no_amp)", "<PERSON><PERSON>(python:*)", "Bash(\"../recommendation_venv_gpu/Scripts/python.exe\" install_gpu_deps.py)", "Bash(\"../recommendation_venv_gpu/Scripts/pip.exe\" uninstall -y torch torchvision torchaudio numpy pandas)", "<PERSON><PERSON>(rmdir:*)", "Bash(rm:*)", "<PERSON><PERSON>(py:*)", "Bash(recommendation_venv_gpu/Scripts/python.exe install_gpu_py39.py)", "Bash(../recommendation_venv_gpu/Scripts/python.exe install_gpu_py39.py)", "Bash(../recommendation_venv_gpu/Scripts/python.exe install_gpu_minimal.py)", "Bash(../recommendation_venv_gpu/Scripts/python.exe test_gpu_quick.py)", "Bash(../recommendation_venv_gpu/Scripts/python.exe -u src/train_loss_optimized.py --model_type dcnv2 --epochs 1 --pos_weight_strategy sqrt_balanced)", "Bash(../recommendation_venv_gpu/Scripts/python.exe -u src/train_loss_optimized.py --model_type dcnv2 --epochs 1 --pos_weight_strategy sqrt_balanced --test_mode)", "Bash(set PYTHONIOENCODING=utf-8)", "Bash(../recommendation_venv_gpu/Scripts/python.exe src/train_loss_optimized.py --help)", "Bash(..recommendation_venvScriptsactivate)", "Bash(call ..recommendation_venvScriptsactivate)", "Bash(c:UsersmathtDownloadswin_projectrecommendationrecommendation_venvScriptspython.exe -u c:UsersmathtDownloadswin_projectrecommendationparallel_experiments_method1srctrain_loss_optimized.py --model_type dcnv2 --epochs 1 --pos_weight_strategy sqrt_balanced)", "Bash(..recommendation_venv_gpuScriptsactivate)", "Bash(ls:*)", "Bash(../recommendation_venv_gpu/Scripts/python.exe -u src/train_loss_optimized.py --model_type dcnv2 --epochs 10 --pos_weight_strategy sqrt_balanced --exclude_groups noise)", "Bash(../recommendation_venv/Scripts/python.exe test_feature_selection.py)", "Bash(../recommendation_venv/Scripts/python.exe -c \"import numpy as np; features = np.load(''processed_data/train_features.npy''); print(f''NPY shape: {features.shape}'')\")", "Bash(../recommendation_venv/Scripts/python.exe src/analyze_data.py:*)", "Bash(../recommendation_venv/Scripts/python.exe:*)", "Bash(../recommendation_venv_gpu/Scripts/python.exe test_feature_manager.py)", "Bash(../recommendation_venv_gpu/Scripts/python.exe src/train_loss_optimized.py --model_type dcnv2 --epochs 1 --pos_weight_strategy sqrt_balanced --include_groups user item)", "Bash(..recommendation_venvScriptspython.exe srcrun_parallel_processing.py --skip-analysis --workers 2)", "Bash(move processed_datafeature_metadata_expanded.json processed_datafeature_metadata_expanded_backup.json)", "Bash(../recommendation_venv_gpu/Scripts/python.exe test_simple_preprocess.py)"], "deny": []}}