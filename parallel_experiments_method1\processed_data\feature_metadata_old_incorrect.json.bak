{"features": [{"index": 0, "name": "conversion", "type": "numeric", "group": "", "original_column": "conversion"}, {"index": 1, "name": "time_spent", "type": "numeric", "group": "time", "original_column": "time_spent"}, {"index": 2, "name": "click_position", "type": "numeric", "group": "click", "original_column": "click_position"}, {"index": 3, "name": "session_length", "type": "numeric", "group": "session", "original_column": "session_length"}, {"index": 4, "name": "context_embedding", "type": "embedding", "group": "context", "original_column": "context_embedding", "embedding_size": 32}, {"index": 5, "name": "age", "type": "numeric", "group": "", "original_column": "age"}, {"index": 6, "name": "gender", "type": "categorical", "group": "", "original_column": "gender"}, {"index": 7, "name": "region", "type": "categorical", "group": "", "original_column": "region"}, {"index": 8, "name": "income_level", "type": "categorical", "group": "income", "original_column": "income_level"}, {"index": 9, "name": "user_embedding", "type": "embedding", "group": "user", "original_column": "user_embedding", "embedding_size": 64}, {"index": 10, "name": "category", "type": "categorical", "group": "", "original_column": "category"}, {"index": 11, "name": "price", "type": "numeric", "group": "", "original_column": "price"}, {"index": 12, "name": "brand", "type": "categorical", "group": "", "original_column": "brand"}, {"index": 13, "name": "rating", "type": "numeric", "group": "", "original_column": "rating"}, {"index": 14, "name": "item_embedding", "type": "embedding", "group": "item", "original_column": "item_embedding", "embedding_size": 64}, {"index": 15, "name": "noise_1", "type": "numeric", "group": "noise", "original_column": "noise_1"}, {"index": 16, "name": "noise_2", "type": "categorical", "group": "noise", "original_column": "noise_2"}], "groups": {"": [0, 5, 6, 7, 10, 11, 12, 13], "time": [1], "click": [2], "session": [3], "context": [4], "income": [8], "user": [9], "item": [14], "noise": [15, 16]}, "total_features": 17, "label_column": "click", "excluded_columns": ["user_id", "item_id", "timestamp"], "original_columns": ["user_id", "item_id", "click", "conversion", "time_spent", "click_position", "session_length", "context_embedding", "age", "gender", "region", "income_level", "user_embedding", "category", "price", "brand", "rating", "item_embedding", "noise_1", "noise_2", "timestamp"]}