"""
Generate feature metadata by analyzing the parquet files and understanding the feature transformation
"""

import os
import json
import pandas as pd
import numpy as np

def generate_feature_metadata():
    """
    Generate feature metadata by reading a sample parquet file and understanding
    how columns are transformed into features
    """
    
    # Read a sample parquet file
    sample_file = r"local_test_data\small\train\data_chunk_000.parquet"
    df = pd.read_parquet(sample_file)
    
    print(f"Sample file columns: {list(df.columns)}")
    print(f"Sample file shape: {df.shape}")
    
    # Define excluded columns and label column
    label_column = "click"
    excluded_columns = ["user_id", "item_id", "timestamp"]
    
    feature_metadata = {
        'features': [],
        'groups': {},
        'total_features': 0,
        'label_column': label_column,
        'excluded_columns': excluded_columns,
        'original_columns': list(df.columns)
    }
    
    feature_index = 0
    
    # Process each column
    for col in df.columns:
        # Skip label and excluded columns
        if col == label_column or col in excluded_columns:
            continue
        
        # Get column prefix/group
        prefix = col.split('_')[0] if '_' in col else ''
        
        # Check if column contains arrays (embeddings)
        sample_value = df[col].iloc[0]
        is_array = isinstance(sample_value, (list, np.ndarray))
        
        if is_array:
            # Array columns are expanded into multiple features
            array_length = len(sample_value)
            
            if prefix not in feature_metadata['groups']:
                feature_metadata['groups'][prefix] = []
            
            # Add one feature for each array element
            for i in range(array_length):
                feature_info = {
                    'index': feature_index,
                    'name': f"{col}_{i}",
                    'type': 'array_element',
                    'group': prefix,
                    'original_column': col,
                    'array_index': i
                }
                feature_metadata['features'].append(feature_info)
                feature_metadata['groups'][prefix].append(feature_index)
                feature_index += 1
                
        else:
            # Single-value columns
            if prefix not in feature_metadata['groups']:
                feature_metadata['groups'][prefix] = []
                
            # Determine type
            if pd.api.types.is_numeric_dtype(df[col]):
                col_type = 'numeric'
            else:
                col_type = 'categorical'
            
            feature_info = {
                'index': feature_index,
                'name': col,
                'type': col_type,
                'group': prefix,
                'original_column': col
            }
            
            feature_metadata['features'].append(feature_info)
            feature_metadata['groups'][prefix].append(feature_index)
            feature_index += 1
    
    feature_metadata['total_features'] = feature_index
    
    # Save metadata
    os.makedirs('processed_data', exist_ok=True)
    metadata_file = 'processed_data/feature_metadata.json'
    
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(feature_metadata, f, indent=2, ensure_ascii=False)
    
    print(f"\nFeature metadata saved to: {metadata_file}")
    print(f"Total features: {feature_metadata['total_features']}")
    print(f"\nFeature groups:")
    
    for group, indices in sorted(feature_metadata['groups'].items()):
        group_name = group if group else "(no prefix)"
        print(f"  {group_name}: {len(indices)} features")
        
    # Show some example features
    print(f"\nFirst 5 features:")
    for feat in feature_metadata['features'][:5]:
        print(f"  {feat['index']}: {feat['name']} (type: {feat['type']}, group: {feat['group'] or 'none'})")
    
    return feature_metadata

if __name__ == "__main__":
    generate_feature_metadata()