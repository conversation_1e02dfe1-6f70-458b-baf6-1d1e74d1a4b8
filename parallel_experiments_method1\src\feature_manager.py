"""
Feature Manager: 统一的特征管理系统，避免所有硬编码索引
"""

import json
import re
import numpy as np
from typing import Dict, List, Optional, Set, Union
import logging

logger = logging.getLogger(__name__)


class FeatureManager:
    """统一的特征管理器，避免所有硬编码"""
    
    def __init__(self, metadata_path: str = 'processed_data/feature_metadata_expanded.json'):
        """
        初始化特征管理器
        
        Args:
            metadata_path: 特征元数据文件路径
        """
        try:
            with open(metadata_path, 'r', encoding='utf-8') as f:
                self.metadata = json.load(f)
            logger.info(f"Loaded feature metadata from {metadata_path}")
            logger.info(f"Total features: {self.metadata['total_features']}")
        except FileNotFoundError:
            logger.error(f"Feature metadata file not found: {metadata_path}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in metadata file: {e}")
            raise
        
        # 构建各种索引
        self._build_indices()
    
    def _build_indices(self):
        """构建各种查找索引"""
        # 1. 名称到索引的映射
        self.name_to_index = {}
        
        # 2. 组到索引列表的映射（直接使用metadata中的groups）
        self.group_to_indices = self.metadata.get('groups', {})
        
        # 3. 原始列到展开特征的映射
        self.original_to_expanded = {}
        
        # 4. 特征类型到索引的映射
        self.type_to_indices = {
            'numeric': [],
            'categorical': [],
            'embedding_element': []
        }
        
        # 5. 索引到特征信息的映射
        self.index_to_feature = {}
        
        # 构建索引
        for feat in self.metadata['features']:
            idx = feat['index']
            name = feat['name']
            feat_type = feat.get('type', 'unknown')
            
            # 名称索引
            self.name_to_index[name] = idx
            
            # 索引到特征信息
            self.index_to_feature[idx] = feat
            
            # 类型索引
            if feat_type in self.type_to_indices:
                self.type_to_indices[feat_type].append(idx)
            
            # 原始列映射
            if 'original_column' in feat:
                orig = feat['original_column']
                if orig not in self.original_to_expanded:
                    self.original_to_expanded[orig] = []
                self.original_to_expanded[orig].append(idx)
        
        logger.debug(f"Built indices: {len(self.name_to_index)} features")
        logger.debug(f"Groups: {list(self.group_to_indices.keys())}")
        logger.debug(f"Original columns: {list(self.original_to_expanded.keys())}")
    
    def get_feature_indices(self, 
                          by_names: Optional[List[str]] = None,
                          by_groups: Optional[List[str]] = None,
                          by_original_columns: Optional[List[str]] = None,
                          by_types: Optional[List[str]] = None,
                          exclude_names: Optional[List[str]] = None,
                          exclude_groups: Optional[List[str]] = None) -> List[int]:
        """
        灵活的特征选择接口
        
        Args:
            by_names: 按特征名称选择（支持通配符*）
            by_groups: 按组选择
            by_original_columns: 按原始列名选择
            by_types: 按特征类型选择
            exclude_names: 排除的特征名称
            exclude_groups: 排除的组
            
        Returns:
            排序的特征索引列表
        """
        all_indices = set(range(self.metadata['total_features']))
        selected = set()
        
        # 按名称选择
        if by_names:
            for name in by_names:
                if '*' in name:
                    # 支持通配符
                    pattern = name.replace('*', '.*')
                    pattern = f"^{pattern}$"  # 完全匹配
                    for feat_name, idx in self.name_to_index.items():
                        if re.match(pattern, feat_name):
                            selected.add(idx)
                elif name in self.name_to_index:
                    selected.add(self.name_to_index[name])
                else:
                    logger.warning(f"Feature name not found: {name}")
        
        # 按组选择
        if by_groups:
            for group in by_groups:
                if group in self.group_to_indices:
                    selected.update(self.group_to_indices[group])
                else:
                    logger.warning(f"Group not found: {group}")
        
        # 按原始列选择
        if by_original_columns:
            for col in by_original_columns:
                if col in self.original_to_expanded:
                    selected.update(self.original_to_expanded[col])
                else:
                    logger.warning(f"Original column not found: {col}")
        
        # 按类型选择
        if by_types:
            for feat_type in by_types:
                if feat_type in self.type_to_indices:
                    selected.update(self.type_to_indices[feat_type])
                else:
                    logger.warning(f"Feature type not found: {feat_type}")
        
        # 如果没有指定选择条件，则选择全部
        if not any([by_names, by_groups, by_original_columns, by_types]):
            selected = all_indices
        
        # 处理排除
        excluded = set()
        
        if exclude_names:
            for name in exclude_names:
                if '*' in name:
                    # 支持通配符
                    pattern = name.replace('*', '.*')
                    pattern = f"^{pattern}$"
                    for feat_name, idx in self.name_to_index.items():
                        if re.match(pattern, feat_name):
                            excluded.add(idx)
                elif name in self.name_to_index:
                    excluded.add(self.name_to_index[name])
        
        if exclude_groups:
            for group in exclude_groups:
                if group in self.group_to_indices:
                    excluded.update(self.group_to_indices[group])
        
        # 返回排序的索引列表
        final_indices = sorted(selected - excluded)
        
        logger.debug(f"Selected {len(final_indices)} features out of {self.metadata['total_features']}")
        
        return final_indices
    
    def get_feature_slice(self, data: np.ndarray, **kwargs) -> np.ndarray:
        """
        直接从数据中切片特征
        
        Args:
            data: 输入数据数组
            **kwargs: 传递给get_feature_indices的参数
            
        Returns:
            切片后的数据
        """
        indices = self.get_feature_indices(**kwargs)
        return data[:, indices]
    
    def get_feature_names(self, indices: Optional[List[int]] = None) -> List[str]:
        """
        获取特征名称
        
        Args:
            indices: 特征索引列表，None表示所有特征
            
        Returns:
            特征名称列表
        """
        if indices is None:
            return [f['name'] for f in self.metadata['features']]
        else:
            names = []
            for idx in indices:
                if idx in self.index_to_feature:
                    names.append(self.index_to_feature[idx]['name'])
                else:
                    names.append(f"unknown_{idx}")
            return names
    
    def get_feature_info(self, index: int) -> Optional[Dict]:
        """获取特定索引的特征信息"""
        return self.index_to_feature.get(index)
    
    def get_group_info(self, group_name: str) -> Dict:
        """获取特定组的信息"""
        if group_name not in self.group_to_indices:
            return {"exists": False}
        
        indices = self.group_to_indices[group_name]
        features = [self.index_to_feature[idx] for idx in indices if idx in self.index_to_feature]
        
        return {
            "exists": True,
            "count": len(indices),
            "indices": indices,
            "features": features,
            "types": list(set(f.get('type', 'unknown') for f in features))
        }
    
    @property
    def total_features(self) -> int:
        """总特征数"""
        return self.metadata['total_features']
    
    @property
    def feature_groups(self) -> List[str]:
        """所有特征组名"""
        return list(self.group_to_indices.keys())
    
    @property
    def feature_types(self) -> List[str]:
        """所有特征类型"""
        return list(self.type_to_indices.keys())
    
    def describe(self):
        """打印特征管理器的描述信息"""
        print(f"Feature Manager Summary:")
        print(f"  Total features: {self.total_features}")
        print(f"\nFeature groups:")
        for group in sorted(self.feature_groups):
            info = self.get_group_info(group)
            group_display = group if group else "(no prefix)"
            print(f"  {group_display}: {info['count']} features")
        
        print(f"\nFeature types:")
        for feat_type, indices in self.type_to_indices.items():
            if indices:
                print(f"  {feat_type}: {len(indices)} features")
        
        print(f"\nOriginal columns with expansions:")
        for col, indices in self.original_to_expanded.items():
            print(f"  {col}: expanded to {len(indices)} features")


class FeatureAccessor:
    """提供便捷的特征访问接口"""
    
    def __init__(self, data: np.ndarray, feature_manager: FeatureManager):
        """
        初始化特征访问器
        
        Args:
            data: 特征数据数组
            feature_manager: 特征管理器实例
        """
        self.data = data
        self.fm = feature_manager
    
    def __getitem__(self, key: Union[str, List[str], int, List[int]]) -> np.ndarray:
        """
        支持多种访问方式
        
        Args:
            key: 可以是特征名、组名、原始列名、索引等
            
        Returns:
            对应的数据切片
        """
        if isinstance(key, str):
            # 尝试作为特征名
            if key in self.fm.name_to_index:
                return self.data[:, self.fm.name_to_index[key]]
            # 尝试作为组名
            elif key in self.fm.group_to_indices:
                indices = self.fm.group_to_indices[key]
                return self.data[:, indices]
            # 尝试作为原始列名
            elif key in self.fm.original_to_expanded:
                indices = self.fm.original_to_expanded[key]
                return self.data[:, indices]
            else:
                raise KeyError(f"Unknown key: {key}")
        
        elif isinstance(key, list):
            # 多个特征名或索引
            if all(isinstance(k, str) for k in key):
                # 特征名列表
                indices = []
                for k in key:
                    if k in self.fm.name_to_index:
                        indices.append(self.fm.name_to_index[k])
                    else:
                        raise KeyError(f"Unknown feature name: {k}")
                return self.data[:, indices]
            elif all(isinstance(k, int) for k in key):
                # 索引列表
                return self.data[:, key]
        
        elif isinstance(key, int):
            # 单个索引
            return self.data[:, key]
        
        # 默认行为（行索引）
        return self.data[key]
    
    @property
    def shape(self) -> tuple:
        """数据形状"""
        return self.data.shape
    
    def get_group(self, group_name: str) -> np.ndarray:
        """获取特定组的特征"""
        return self[group_name]
    
    def get_original_column(self, column_name: str) -> np.ndarray:
        """获取原始列对应的所有展开特征"""
        if column_name in self.fm.original_to_expanded:
            indices = self.fm.original_to_expanded[column_name]
            return self.data[:, indices]
        else:
            raise KeyError(f"Original column not found: {column_name}")


# 便捷函数
def create_feature_manager(metadata_path: Optional[str] = None) -> FeatureManager:
    """创建特征管理器的便捷函数"""
    if metadata_path is None:
        # 尝试默认路径
        import os
        default_paths = [
            'processed_data/feature_metadata_expanded.json',
            '../processed_data/feature_metadata_expanded.json',
            '../../processed_data/feature_metadata_expanded.json'
        ]
        
        for path in default_paths:
            if os.path.exists(path):
                metadata_path = path
                break
        
        if metadata_path is None:
            raise FileNotFoundError(
                "Cannot find feature metadata file. "
                "Please specify the path explicitly."
            )
    
    return FeatureManager(metadata_path)