"""Test metadata generation in preprocess.py with debug"""

import sys
import os
import json
sys.path.append('src')

from preprocess import load_analysis_results, IntelligentPreprocessor

def test_metadata_generation():
    print("Testing metadata generation with debug...")
    
    # Load analysis results
    analysis_results = load_analysis_results()
    if not analysis_results:
        print("ERROR: Could not load analysis results")
        return False
    
    # Debug: check what's in the analysis results
    print("\nDEBUG: Checking analysis results structure...")
    if 'summary' in analysis_results:
        summary = analysis_results['summary']
        print(f"  Array columns: {summary.get('array_columns', [])}")
        print(f"  Numeric columns count: {len(summary.get('numeric_columns', []))}")
        print(f"  Categorical columns count: {len(summary.get('categorical_columns', []))}")
    
    # Check column_analysis
    if 'column_analysis' in analysis_results:
        print("\nDEBUG: Checking column_analysis...")
        for col in ['user_embedding', 'item_embedding', 'context_embedding']:
            if col in analysis_results['column_analysis']:
                col_info = analysis_results['column_analysis'][col]
                print(f"  {col}: array_length = {col_info.get('array_length', 'NOT FOUND')}")
    
    # Create preprocessor
    preprocessor = IntelligentPreprocessor(analysis_results)
    
    # Debug array dimensions
    print("\nDEBUG: Array dimensions detected:")
    for col, dim in preprocessor.array_dimensions.items():
        print(f"  {col}: {dim}")
    
    # Check metadata structure
    print(f"\nMetadata initialized:")
    print(f"  Total expected features: {preprocessor.feature_index}")
    print(f"  Feature groups: {len(preprocessor.feature_metadata['groups'])}")
    
    # Show group breakdown
    print("\nFeature groups:")
    for group, indices in sorted(preprocessor.feature_metadata['groups'].items()):
        group_name = group if group else "(no prefix)"
        print(f"  {group_name}: {len(indices)} features")
    
    # Check array expansion
    print(f"\nArray features expanded:")
    array_features = [f for f in preprocessor.feature_metadata['features'] if f['type'] == 'embedding_element']
    print(f"  Total expanded features: {len(array_features)}")
    if array_features:
        print(f"  First expanded feature: {array_features[0]}")
        print(f"  Last expanded feature: {array_features[-1]}")
    
    return True

if __name__ == "__main__":
    success = test_metadata_generation()
    if success:
        print("\n[OK] Metadata generation test completed!")
    else:
        print("\n[FAIL] Metadata generation test failed!")