"""
Verify if array columns are expanded in NPY files
"""
import numpy as np
import pandas as pd
import json

# Load a sample parquet file to check original structure
sample_file = r"local_test_data\small\train\data_chunk_000.parquet"
df = pd.read_parquet(sample_file)

print("Original Parquet columns:")
print(f"Total columns: {len(df.columns)}")
print(f"Columns: {list(df.columns)}")

# Check array columns
array_columns = []
for col in df.columns:
    sample_value = df[col].iloc[0]
    if isinstance(sample_value, (list, np.ndarray)):
        array_columns.append((col, len(sample_value)))
        
print(f"\nArray columns found:")
for col, dim in array_columns:
    print(f"  {col}: {dim} dimensions")

# Expected features after expansion
expected_features = 0
# Non-array columns (excluding label and excluded columns)
excluded_columns = ["user_id", "item_id", "timestamp", "click"]
non_array_features = len([col for col in df.columns if col not in excluded_columns and not any(col == ac[0] for ac in array_columns)])
expected_features += non_array_features

# Add expanded array features
for col, dim in array_columns:
    if col not in excluded_columns:
        expected_features += dim

print(f"\nExpected feature count after expansion:")
print(f"  Non-array features: {non_array_features}")
print(f"  Array features expanded: {sum(dim for col, dim in array_columns if col not in excluded_columns)}")
print(f"  Total expected: {expected_features}")

# Load actual NPY file
try:
    train_features = np.load('processed_data/train_features.npy')
    print(f"\nActual NPY shape: {train_features.shape}")
    print(f"Actual feature count: {train_features.shape[1]}")
    
    if train_features.shape[1] == expected_features:
        print("\n✓ Arrays ARE expanded! NPY has the expected number of features.")
    elif train_features.shape[1] == len(df.columns) - len(excluded_columns):
        print("\n✗ Arrays are NOT expanded! NPY has same feature count as original columns.")
    else:
        print(f"\n? Unexpected feature count. NPY has {train_features.shape[1]} features.")
        
except FileNotFoundError:
    print("\nNPY file not found. Please run preprocessing first.")
    
# Check feature metadata
try:
    with open('processed_data/feature_metadata_expanded.json', 'r') as f:
        metadata = json.load(f)
    print(f"\nFeature metadata says: {metadata['total_features']} features")
    print(f"Feature count in metadata: {len(metadata['features'])}")
except FileNotFoundError:
    print("\nFeature metadata not found.")