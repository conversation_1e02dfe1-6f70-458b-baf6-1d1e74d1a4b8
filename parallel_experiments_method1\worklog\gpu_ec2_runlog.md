```text
2025-08-02 06:34:18,397 - TRACK - [PID:53366] S3_REQ:[CONNECTION_STATS pid=53366 process_conn=0 total=42 active=42 failed=0 retries=0 worker_53366_start
2025-08-02 06:34:18,398 - INFO - [PID:53366] worker_53366 START FILE_PROCESS
2025-08-02 06:34:18,398 - INFO - [PID:53366] 进程 53366 开始预处理文件: s
2025-08-02 06:34:18,398 - INFO - [PID:53366] 进程 53366 开始预处理...
2025-08-02 06:34:18,398 - TRACK - [S3_REQ:[CONNECTION_STATS pid=53366 process_conn=0 total=42 active=42 failed=0 retries=0 worker_53366_start
2025-08-02 06:34:18,398 - TRACK - [PID:53366] S3_REQ:[CONNECTION_STATS pid=53366 process_conn=0 total=42 active=42 failed=0 retries=0 worker_53366_start
2025-08-02 06:34:18,398 - INFO - [PID:53366] [进程维度] 53366 before_process 链接维持数:0, 全局总链接:42, 活跃连接:42, 失败连接:0, 重试次数:0 worker_53366_start
2025-08-02 06:34:18,398 - INFO - [PID:53366] worker_53366 配置:
2025-08-02 06:34:18,398 - INFO - [PID:53366] 任务ID: 15
2025-08-02 06:34:18,398 - INFO - [PID:53366] 数据集: spain
2025-08-02 06:34:18,398 - INFO - [PID:53366] 文本大小检查开始...
2025-08-02 06:34:18,398 - INFO - [PID:53366] 进程 53366 创建预处理器...
2025-08-02 06:34:18,398 - INFO - [PID:53366] 进程 53366 创建预处理器...
2025-08-02 06:34:18,398 - TRACK - [PID:50767] THREAD_CREATE: THREAD_59_FILE_PROCESS
2025-08-02 06:34:18,398 - INFO - [PID:53366] 预处理器初始化完成:
2025-08-02 06:34:18,398 - INFO - [PID:53366] 预估列: None
2025-08-02 06:34:18,398 - INFO - [PID:53366] 敏感列: 10 个
2025-08-02 06:34:18,398 - TRACK - [PID:50767] THREAD_CREATE: THREAD_60_FILE_PROCESS
2025-08-02 06:34:18,398 - TRACK - [PID:50767] THREAD_CREATE: THREAD_60_FILE_PROCESS
2025-08-02 06:34:18,398 - INFO - [PID:53366] 微调列: 158 个
2025-08-02 06:34:18,398 - INFO - [PID:53366] 丢弃列: None
2025-08-02 06:34:18,398 - INFO - [PID:50767] 已提交 60/100 个任务
2025-08-02 06:34:18,398 - INFO - [PID:53366] 进程 53366 预处理器创建成功
2025-08-02 06:34:18,398 - INFO - [PID:53366] 进程 53366 开始成功
2025-08-02 06:34:18,398 - TRACK - S3_REQ: READ_FILE worker-53366 file-path: 00015-51bc3968-12f5-4dc8-adcc-8e586c4ec4ef-c000.snappy.parquet
2025-08-02 06:34:18,398 - TRACK - [PID:53366] S3_REQ: READ_FILE worker-53366 file-path: 00015-51bc3968-12f5-4dc8-adcc-8e586c4ec4ef-c000.snappy.parquet
2025-08-02 06:34:18,398 - INFO - [PID:53366] 进程 53366 开始处理parquet文件:
... 进程 53366 开始处理parquet文件:
2025-08-02 06:34:18,398 - TRACK - S3_REQ:[CONNECTION_STATS pid=53366 process_conn=0 total=42 active=42 failed=0 retries=0 worker_53366_before_process
2025-08-02 06:34:18,398 - TRACK - [PID:53366] S3_REQ:[CONNECTION_STATS pid=53366 process_conn=0 total=42 active=42 failed=0 retries=0 worker_53366_before_process
2025-08-02 06:34:18,398 - TRACK - [PID:50767] THREAD_CREATE: THREAD_61_FILE_PROCESS
2025-08-02 06:34:18,398 - INFO - [PID:53366] [进程维度] 53366_before_process 链接维持数:0, 全局总链接:42, 活跃连接:42, 失败连接:0, 重试次数:0 worker_53366_before_process
2025-08-02 06:34:18,398 - TRACK - [PID:50767] THREAD_CREATE: THREAD_61_FILE_PROCESS
2025-08-02 06:34:18,398 - INFO - [PID:53366] ... 进程 53366 开始调用 process_parquet_chunk...
... 进程 53366 开始调用 process_parquet_chunk...
2025-08-02 06:34:18,398 - INFO - [PID:53366] is read_parquet_file called with:
2025-08-02 06:34:18,398 - TRACK - [PID:50767] THREAD_CREATE: THREAD_62_FILE_PROCESS
2025-08-02 06:34:18,398 - INFO - [PID:53366] is_s3 path: True
2025-08-02 06:34:18,398 - TRACK - [PID:50767] THREAD_CREATE: THREAD_62_FILE_PROCESS
2025-08-02 06:34:18,398 - INFO - [PID:53366] Additional kwargs: {}
2025-08-02 06:34:18,399 - INFO - [PID:53366] Processing as s3 file...
2025-08-02 06:34:18,399 - INFO - [PID:53366] is_read_s3_filesystem called ---
2025-08-02 06:34:18,399 - INFO - [PID:53366] Process ID: 53366
2025-08-02 06:34:18,399 - TRACK - [PID:50767] THREAD_CREATE: THREAD_63_FILE_PROCESS
2025-08-02 06:34:18,399 - INFO - [PID:53366] S3_AVAILABLE: True
2025-08-02 06:34:18,399 - INFO - [PID:53366] Using existing S3 filesystem instance for process 53366
2025-08-02 06:34:18,399 - INFO - [PID:53366] s3 filesystem obtained, calling read_parquet...
2025-08-02 06:34:18,399 - INFO - [PID:53366] s3_filesystem.read_parquet called [PID:53366] ===
2025-08-02 06:34:18,399 - INFO - [PID:53366] s3_path:
2025-08-02 06:34:18,399 - INFO - [PID:53366] Kwargs: {}
2025-08-02 06:34:18,399 - TRACK - S3_REQ:[CONNECTION_STATS pid=53366 process_conn=0 total=42 active=42 failed=0 retries=0 before_read_parquet
2025-08-02 06:34:18,399 - TRACK - [PID:53366] S3_REQ:[CONNECTION_STATS pid=53366 process_conn=0 total=42 active=42 failed=0 retries=0 before_read_parquet
2025-08-02 06:34:18,399 - INFO - [PID:53366] [进程维度] [PID:53366] ... 链接维持数:0, 全局总链接:42, 活跃连接:42, 失败连接:0, 重试次数:0 before_read_parquet
2025-08-02 06:34:18,399 - INFO - [PID:53366] Creating process-local s3fs instance for fork-safe operation
2025-08-02 06:34:18,399 - INFO - [PID:53366] Creating process-local s3fs instance for fork-safe operation [PID:53366]
2025-08-02 06:34:18,399 - INFO - [PID:53366] Creating process-local s3fs for PID: 53366
2025-08-02 06:34:18,399 - TRACK - S3_REQ:[CONNECTION_STATS pid=53366 process_conn=0 total=42 active=42 failed=0 retries=0 before_local_fs_create
2025-08-02 06:34:18,399 - TRACK - [PID:53366] S3_REQ:[CONNECTION_STATS pid=53366 process_conn=0 total=42 active=42 failed=0 retries=0 before_local_fs_create
2025-08-02 06:34:18,399 - INFO - [PID:53366] [进程维度] [PID:53366] ... 链接维持数:0, 全局总链接:42, 活跃连接:42, 失败连接:0, 重试次数:0 before_local_fs_create
2025-08-02 06:34:18,399 - INFO - [PID:53366] process-local fs create [PID:53366] (attempt 1/3): {'use_ssl': True, 'client_kwargs': {'region_name': 'us-east-1'}}
2025-08-02 06:34:18,399 - TRACK - WORKER-53367 START FILE_PROCESS
2025-08-02 06:34:18,399 - TRACK - [PID:53367] WORKER-53367 START FILE_PROCESS
2025-08-02 06:34:18,399 - INFO - [PID:53367] 进程 53367 开始处理文件:
2025-08-02 06:34:18,399 - INFO - 进程 53367 开始处理文件:
2025-08-02 06:34:18,399 - TRACK - S3_REQ:[CONNECTION_STATS pid=53367 process_conn=0 total=42 active=42 failed=0 retries=0 worker_53367_start
2025-08-02 06:34:18,399 - TRACK - [PID:53367] S3_REQ:[CONNECTION_STATS pid=53367 process_conn=0 total=42 active=42 failed=0 retries=0 worker_53367_start
2025-08-02 06:34:18,400 - INFO - [PID:53367] [进程维度] [PID:53367] ... 链接维持数:0, 全局总链接:42, 活跃连接:42, 失败连接:0, 重试次数:0 worker_53367_start
2025-08-02 06:34:18,400 - INFO - [PID:53367] worker_53367 配置:
2025-08-02 06:34:18,400 - INFO - [PID:53367] 任务ID: 16
2025-08-02 06:34:18,400 - INFO - [PID:53367] 数据集: spain
2025-08-02 06:34:18,400 - INFO - [PID:53367] 文本大小检查开始...
2025-08-02 06:34:18,400 - INFO - [PID:53367] 进程 53367 创建预处理器...
2025-08-02 06:34:18,400 - INFO - [PID:53367]
2025-08-02 06:34:18,400 - INFO - [PID:53367] 预处理器初始化完成:
2025-08-02 06:34:18,400 - INFO - [PID:53367] 预估列: None
2025-08-02 06:34:18,400 - INFO - [PID:53367] 敏感列: 10 个
2025-08-02 06:34:18,400 - INFO - [PID:53367] 微调列: 158 个
```
.
.
.

2025-08-02 06:34:18,408 - INFO - [PID:53370] 文本大小检查开始...
2025-08-02 06:34:18,408 - INFO - [PID:53370] 进程 53370 创建预处理器...
2025-08-02 06:34:18,408 - INFO - [PID:53370]
2025-08-02 06:34:18,408 - INFO - [PID:53370] 预处理器初始化完成:
2025-08-02 06:34:18,408 - TRACK - [PID:50767] THREAD_CREATE: THREAD_90_FILE_PROCESS
2025-08-02 06:34:18,408 - TRACK - [PID:50767] THREAD_CREATE: THREAD_90_FILE_PROCESS
2025-08-02 06:34:18,409 - INFO - [PID:53370] 预估列: None
2025-08-02 06:34:18,409 - INFO - [PID:53370] 敏感列: 10 个
2025-08-02 06:34:18,409 - INFO - [PID:50767] 已提交 90/100 个任务
2025-08-02 06:34:18,409 - INFO - [PID:53370] 微调列: 158 个
2025-08-02 06:34:18,409 - INFO - [PID:53370] 丢弃列: 3 个
2025-08-02 06:34:18,409 - INFO - [PID:53370] 进程 53370 预处理器创建成功
2025-08-02 06:34:18,409 - INFO - 进程 53370 预处理器创建成功
2025-08-02 06:34:18,409 - TRACK - S3_REQ: READ_FILE worker-53370 file=xxx-c000.snappy.parquet
2025-08-02 06:34:18,409 - TRACK - [PID:53370] S3_REQ: READ_FILE worker-53370 file=xxx-c000.snappy.parquet
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_91_FILE_PROCESS
2025-08-02 06:34:18,409 - INFO - [PID:53370] 进程 53370 开始处理parquet文件: s3://xxx-c000.snappy.parquet
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_91_FILE_PROCESS
... 进程 53370 开始处理parquet文件: s3://xxx.snappy.parquet
2025-08-02 06:34:18,409 - TRACK - S3_REQ:[CONNECTION_STATS pid=53370 process_conn=0 total=42 active=42 failed=0 retries=0 worker_53370_before_process
2025-08-02 06:34:18,409 - TRACK - [PID:53370] S3_REQ:[CONNECTION_STATS pid=53370 process_conn=0 total=42 active=42 failed=0 retries=0 worker_53370_before_process
2025-08-02 06:34:18,409 - INFO - [PID:53370] [进程维度] 53370 [PID:53370] 进程链接数:0, 全局总链接:42, 活跃连接:42, 失败连接:0, 重试次数:0 worker_53370_before_process
2025-08-02 06:34:18,409 - INFO - [PID:53370] ... 进程 53370 开始调用 process_parquet_chunk...
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_92_FILE_PROCESS
... 进程 53370 开始调用 process_parquet_chunk...
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_92_FILE_PROCESS
2025-08-02 06:34:18,409 - TRACK - [PID:53370] is read_parquet_file called with: s3://xxx.c000.snappy.parquet ---
2025-08-02 06:34:18,409 - INFO - [PID:53370] Is s3 path: True
2025-08-02 06:34:18,409 - INFO - [PID:53370] Additional kwargs: {}
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_93_FILE_PROCESS
2025-08-02 06:34:18,409 - INFO - [PID:53370] Processing as s3 file...
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_93_FILE_PROCESS
2025-08-02 06:34:18,409 - INFO - [PID:53370] --- get_s3_filesystem called ---
2025-08-02 06:34:18,409 - INFO - [PID:53370] Process ID: 53370
2025-08-02 06:34:18,409 - INFO - [PID:53370] S3_AVAILABLE: True
2025-08-02 06:34:18,409 - INFO - [PID:53370] Using existing S3 filesystem instance for process 53370
2025-08-02 06:34:18,409 - INFO - [PID:53370] S3 filesystem obtained, calling read_parquet...
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_94_FILE_PROCESS
2025-08-02 06:34:18,409 - INFO - [PID:53370] --- s3_filesystem.read_parquet called [PID:53370] ---
2025-08-02 06:34:18,409 - INFO - [PID:53370] s3_path: s3://xxx.snappy.parquet
2025-08-02 06:34:18,409 - INFO - [PID:53370] Kwargs: {}
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_94_FILE_PROCESS
2025-08-02 06:34:18,409 - TRACK - S3_REQ:[CONNECTION_STATS pid=53370 process_conn=0 total=42 active=42 failed=0 retries=0 before_read_parquet
2025-08-02 06:34:18,409 - TRACK - [PID:53370] S3_REQ:[CONNECTION_STATS pid=53370 process_conn=0 total=42 active=42 failed=0 retries=0 before_read_parquet
2025-08-02 06:34:18,409 - INFO - [PID:53370] [进程维度] 53370 [PID:53370] 进程链接数:0, 全局总链接:42, 活跃连接:42, 失败连接:0, 重试次数:0 before_read_parquet
2025-08-02 06:34:18,409 - TRACK - [PID:53370] Reading parquet file...[PID:53370] (attempt 1/3): s3://xxx.parquet
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_95_FILE_PROCESS
2025-08-02 06:34:18,409 - INFO - [PID:53370] Creating process-local s3fs instance for fork-safe operation [PID:53370]
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_95_FILE_PROCESS
2025-08-02 06:34:18,409 - INFO - [PID:53370] Creating process-local s3fs for PID: 53370
2025-08-02 06:34:18,409 - TRACK - S3_REQ:[CONNECTION_STATS pid=53370 process_conn=0 total=42 active=42 failed=0 retries=0 before_local_fs_create
2025-08-02 06:34:18,409 - TRACK - [PID:53370] S3_REQ:[CONNECTION_STATS pid=53370 process_conn=0 total=42 active=42 failed=0 retries=0 before_local_fs_create
2025-08-02 06:34:18,409 - INFO - [PID:53370] [进程维度] 53370 [PID:53370] 进程链接数:0, 全局总链接:42, 活跃连接:42, 失败连接:0, 重试次数:0 before_local_fs_create
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_96_FILE_PROCESS
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_96_FILE_PROCESS
2025-08-02 06:34:18,409 - INFO - [PID:53370] process_local_fs_kwargs [PID:53370] (attempt 1/3): {'use_ssl': True, 'client_kwargs': {'region_name': 'us-east-1'}}
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_97_FILE_PROCESS
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_97_FILE_PROCESS
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_98_FILE_PROCESS
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_98_FILE_PROCESS
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_99_FILE_PROCESS
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_99_FILE_PROCESS
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_100_FILE_PROCESS
2025-08-02 06:34:18,409 - TRACK - [PID:50767] THREAD_CREATE: THREAD_100_FILE_PROCESS
2025-08-02 06:34:18,410 - INFO - [PID:50767] CHECK 100/100 任务
2025-08-02 06:34:18,410 - TRACK - POOL_STATE: 20_ALL_SUBMITTED total=20 active-1 total-20 ALL_SUBMITTED
2025-08-02 06:34:18,410 - TRACK - [PID:50767] POOL_STATE: 20_ALL_SUBMITTED total=20 active-1 total-20 ALL_SUBMITTED
2025-08-02 06:34:18,410 - INFO - [PID:50767] 所有任务已成功提交，开始获取结果...
2025-08-02 06:34:18,410 - TRACK - RESP: TASKS_SUBMIT_COUNT_100 (total: 1)
2025-08-02 06:34:18,410 - TRACK - [PID:50767] RESP: TASKS_SUBMIT_COUNT_100 (total: 1)
2025-08-02 06:34:18,410 - TRACK - S3_REQ:[CONNECTION_STATS pid=53370 process_conn=0 total=42 active=42 failed=0 retries=0 after_tasks_submitted
2025-08-02 06:34:18,410 - TRACK - [PID:50767] S3_REQ:[CONNECTION_STATS pid=50767 process_conn=0 total=42 active=42 failed=0 retries=0 after_tasks_submitted
2025-08-02 06:34:18,410 - INFO - [PID:50767] [进程维度] 50767 [PID:50767] 进程链接数:0, 全局总链接:42, 活跃连接:42, 失败连接:0, 重试次数:0 after_tasks_submitted
2025-08-02 06:34:18,410 - INFO - [PID:53370] process_local_fs create successfully [PID:53370] in 0.00s
2025-08-02 06:34:18,410 - TRACK - S3_REQ:[CONNECTION_STATS pid=53370 process_conn=1 total=43 active=43 failed=0 retries=0 after_local_fs_create_success
2025-08-02 06:34:18,410 - TRACK - [PID:53370] S3_REQ:[CONNECTION_STATS pid=53370 process_conn=1 total=43 active=43 failed=0 retries=0 after_local_fs_create_success
2025-08-02 06:34:18,410 - INFO - [PID:53370] 连接数审计 [PID:53370] - 进程连接数:1, 全局总连接:43, 活跃连接:43, 失败连接:0, 重试次数:0 after_local_fs_create_success