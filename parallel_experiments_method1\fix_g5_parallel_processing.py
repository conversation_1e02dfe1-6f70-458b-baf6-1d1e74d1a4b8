#!/usr/bin/env python3
"""
修复g5.24xlarge EC2实例上并行处理CPU利用率低的问题

问题诊断：
1. 代码硬编码限制max_workers为20
2. 进程大部分时间在等待S3 I/O
3. g5实例被误判为需要GPU优化配置
"""

import os
import sys

def analyze_and_fix():
    """分析并修复并行处理问题"""
    
    print("="*60)
    print("g5.24xlarge并行处理问题分析与修复")
    print("="*60)
    
    print("\n问题诊断：")
    print("1. parallel_processor.py限制max_workers最多20个")
    print("2. g5实例有96个vCPU，但只用了约20%")
    print("3. 进程可能在等待S3 I/O，需要更多并发")
    
    print("\n修复方案：")
    
    # 方案1：修改parallel_processor.py
    print("\n方案1：修改worker数限制")
    print("编辑 src/parallel_processor.py，找到以下代码：")
    print("""
        if self.max_workers > 20:  # 如果worker数过多，限制为20
            self.max_workers = 20
    """)
    print("修改为：")
    print("""
        # EC2环境可以使用更多worker
        if os.getenv('IS_EC2_DEPLOYMENT', 'false').lower() == 'true':
            # g5.24xlarge有96个vCPU，可以安全使用更多worker
            if self.max_workers > 64:  # EC2上限制为64
                self.max_workers = 64
                logger.warning(f"🔧 EC2 S3并发优化: 将worker数从 {original_max_workers} 限制为 {self.max_workers}")
        else:
            # 非EC2环境保持原有限制
            if self.max_workers > 20:
                self.max_workers = 20
    """)
    
    # 方案2：优化S3连接
    print("\n方案2：优化S3连接池")
    print("设置环境变量增加S3连接池大小：")
    print("""
export AWS_MAX_POOL_CONNECTIONS=100
export IS_EC2_DEPLOYMENT=true
    """)
    
    # 方案3：调整配置
    print("\n方案3：强制使用更多worker")
    print("运行时使用环境变量：")
    print("""
export FORCE_NUM_WORKERS=48
export IS_EC2_DEPLOYMENT=true
python src/run_parallel_processing.py --workers 48
    """)
    
    # 方案4：检查实例配置
    print("\n方案4：检查g5实例配置")
    print("修改 src/config.py，确保g5.24xlarge被正确识别：")
    print("""
    elif cpu_count >= 90:  # g5.24xlarge也是96 vCPU
        return {
            'instance_type': 'g5.24xlarge',  
            'max_workers': 64,  # 增加到64
            'chunk_rows': 200_000,
            'memory_limit_gb': 350,  # g5.24xlarge有384GB内存
            'batch_size_multiplier': 4,
            'io_workers': 48,  # 增加I/O并发
        }
    """)
    
    print("\n建议的执行步骤：")
    print("1. 首先尝试方案3（最简单）")
    print("2. 如果还是不行，执行方案1修改代码")
    print("3. 同时应用方案2和4进行优化")
    
    print("\n监控命令：")
    print("# 查看CPU使用率")
    print("top -H  # 查看所有线程")
    print("htop    # 更好的可视化")
    print("\n# 查看进程数")
    print("ps aux | grep python | wc -l")
    print("\n# 查看S3连接")
    print("netstat -an | grep :443 | wc -l")

if __name__ == "__main__":
    analyze_and_fix()