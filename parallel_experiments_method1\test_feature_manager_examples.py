"""Example usage cases for FeatureManager"""

import sys
import os
import numpy as np
sys.path.append('src')

from feature_manager import FeatureManager, FeatureAccessor

def example_basic_usage():
    """Basic usage examples"""
    print("\n" + "="*60)
    print("EXAMPLE 1: Basic Feature Selection")
    print("="*60)
    
    manager = FeatureManager('processed_data/feature_metadata_expanded.json')
    
    # Get all features
    all_indices = manager.get_feature_indices()
    print(f"\nTotal features available: {len(all_indices)}")
    
    # Select specific groups
    user_features = manager.get_feature_indices(by_groups=['user'])
    print(f"User features: {len(user_features)} features")
    
    # Exclude certain groups
    no_noise = manager.get_feature_indices(exclude_groups=['noise'])
    print(f"Features without noise: {len(no_noise)} features")
    
    # Get feature names
    user_names = manager.get_feature_names(user_features)
    print(f"\nFirst 5 user feature names: {user_names[:5]}")

def example_wildcard_selection():
    """Wildcard pattern examples"""
    print("\n" + "="*60)
    print("EXAMPLE 2: Wildcard Pattern Selection")
    print("="*60)
    
    manager = FeatureManager('processed_data/feature_metadata_expanded.json')
    
    # Select all embeddings
    embeddings = manager.get_feature_indices(by_names=['*embedding*'])
    print(f"\nAll embedding features: {len(embeddings)} features")
    
    # Select specific embedding indices
    first_dims = manager.get_feature_indices(by_names=['*_embedding_0'])
    print(f"First dimension of each embedding: {len(first_dims)} features")
    print(f"Names: {manager.get_feature_names(first_dims)}")
    
    # Select features ending with specific numbers
    ending_1_or_2 = manager.get_feature_indices(by_names=['*_1', '*_2'])
    print(f"\nFeatures ending with _1 or _2: {len(ending_1_or_2)} features")
    names = manager.get_feature_names(ending_1_or_2)
    print(f"Names: {names}")

def example_combined_selection():
    """Complex selection criteria examples"""
    print("\n" + "="*60)
    print("EXAMPLE 3: Combined Selection Criteria")
    print("="*60)
    
    manager = FeatureManager('processed_data/feature_metadata_expanded.json')
    
    # Select embeddings but exclude first few dimensions
    indices = manager.get_feature_indices(
        by_types=['embedding_element'],
        exclude_names=['*_embedding_0', '*_embedding_1', '*_embedding_2']
    )
    print(f"\nEmbeddings excluding first 3 dimensions: {len(indices)} features")
    
    # Select user and item features but not embeddings
    indices = manager.get_feature_indices(
        by_groups=['user', 'item'],
        exclude_types=['embedding_element']
    )
    print(f"User/item non-embedding features: {len(indices)} features")
    # Note: This might be 0 if user/item groups only contain embeddings

def example_data_access():
    """Using FeatureAccessor for data manipulation"""
    print("\n" + "="*60)
    print("EXAMPLE 4: Data Access with FeatureAccessor")
    print("="*60)
    
    manager = FeatureManager('processed_data/feature_metadata_expanded.json')
    
    # Create dummy data
    n_samples = 1000
    n_features = manager.total_features
    data = np.random.randn(n_samples, n_features).astype(np.float32)
    
    # Create accessor
    accessor = FeatureAccessor(data, manager)
    
    # Access by group
    user_data = accessor['user']
    print(f"\nUser data shape: {user_data.shape}")
    
    # Access by feature name
    if 'age' in manager.name_to_index:
        age_data = accessor['age']
        print(f"Age data shape: {age_data.shape}")
        print(f"Age data sample: {age_data[:5]}")
    
    # Access by original column (gets all expanded features)
    user_embedding = accessor['user_embedding']
    print(f"\nUser embedding shape: {user_embedding.shape}")

def example_training_integration():
    """Integration with training pipeline"""
    print("\n" + "="*60)
    print("EXAMPLE 5: Training Pipeline Integration")
    print("="*60)
    
    manager = FeatureManager('processed_data/feature_metadata_expanded.json')
    
    # Scenario 1: Train without noise features
    clean_indices = manager.get_feature_indices(exclude_groups=['noise'])
    print(f"\nScenario 1 - Clean training:")
    print(f"  Selected features: {len(clean_indices)}")
    print(f"  Excluded: noise features")
    
    # Scenario 2: Embedding-only model
    embedding_indices = manager.get_feature_indices(by_types=['embedding_element'])
    print(f"\nScenario 2 - Embedding-only model:")
    print(f"  Selected features: {len(embedding_indices)}")
    print(f"  Feature groups: user, item, context embeddings")
    
    # Scenario 3: Traditional features only
    traditional_indices = manager.get_feature_indices(
        exclude_types=['embedding_element']
    )
    print(f"\nScenario 3 - Traditional features:")
    print(f"  Selected features: {len(traditional_indices)}")
    print(f"  Types: numeric and categorical features")
    
    # Scenario 4: Specific feature combination
    custom_indices = manager.get_feature_indices(
        by_groups=['user', 'item'],
        exclude_names=['*_embedding_6?']  # Exclude some dimensions
    )
    print(f"\nScenario 4 - Custom selection:")
    print(f"  Selected features: {len(custom_indices)}")
    print(f"  Groups: user and item (with some exclusions)")

def example_feature_analysis():
    """Analyzing feature structure"""
    print("\n" + "="*60)
    print("EXAMPLE 6: Feature Structure Analysis")
    print("="*60)
    
    manager = FeatureManager('processed_data/feature_metadata_expanded.json')
    
    # Analyze groups
    print("\nFeature groups:")
    for group in sorted(manager.feature_groups):
        info = manager.get_group_info(group)
        group_name = group if group else "(no prefix)"
        print(f"  {group_name}: {info['count']} features")
        if info['exists']:
            print(f"    Types: {info['types']}")
    
    # Analyze types
    print("\nFeature types:")
    for feat_type in sorted(manager.feature_types):
        indices = manager.get_feature_indices(by_types=[feat_type])
        print(f"  {feat_type}: {len(indices)} features")
    
    # Find specific patterns
    print("\nSpecific patterns:")
    patterns = [
        ('user_embedding_[0-9]$', 'Single digit user embeddings'),
        ('.*_embedding_[0-3]$', 'First 4 dimensions of any embedding'),
        ('^[^_]+$', 'Features with no underscore'),
    ]
    
    for pattern, desc in patterns:
        indices = manager.get_feature_indices(by_names=[pattern])
        print(f"  {desc}: {len(indices)} features")

def example_practical_scenarios():
    """Real-world practical scenarios"""
    print("\n" + "="*60)
    print("EXAMPLE 7: Practical ML Scenarios")
    print("="*60)
    
    manager = FeatureManager('processed_data/feature_metadata_expanded.json')
    
    # Load actual data (dummy for example)
    n_samples = 10000
    X = np.random.randn(n_samples, manager.total_features).astype(np.float32)
    y = np.random.randint(0, 2, n_samples)
    
    # Scenario 1: Feature ablation study
    print("\nFeature Ablation Study:")
    groups_to_test = [['user'], ['item'], ['context'], ['user', 'item'], 
                      ['user', 'item', 'context']]
    
    for groups in groups_to_test:
        indices = manager.get_feature_indices(by_groups=groups)
        X_subset = X[:, indices]
        print(f"  Groups {groups}: {X_subset.shape[1]} features")
        # Here you would train a model with X_subset
    
    # Scenario 2: Dimensionality reduction
    print("\nDimensionality Reduction:")
    # Select every 4th embedding dimension
    reduced_indices = []
    for i in range(0, 64, 4):  # Assuming 64-dim embeddings
        pattern = f'*_embedding_{i}'
        reduced_indices.extend(manager.get_feature_indices(by_names=[pattern]))
    
    print(f"  Original embedding features: 160")
    print(f"  Reduced embedding features: {len(reduced_indices)}")
    print(f"  Reduction ratio: {len(reduced_indices)/160:.2%}")
    
    # Scenario 3: Feature importance analysis
    print("\nFeature Importance Analysis Setup:")
    # After training, you could analyze which groups are most important
    feature_importance = np.random.rand(manager.total_features)  # Dummy importance
    
    for group in ['user', 'item', 'context', 'noise', '']:
        indices = manager.get_feature_indices(by_groups=[group])
        if indices:
            avg_importance = np.mean(feature_importance[indices])
            group_name = group if group else "(no prefix)"
            print(f"  {group_name}: avg importance = {avg_importance:.3f}")

def run_all_examples():
    """Run all examples"""
    print("\n" + "="*80)
    print("FEATURE MANAGER COMPREHENSIVE EXAMPLES")
    print("="*80)
    
    # Check if metadata exists
    if not os.path.exists('processed_data/feature_metadata_expanded.json'):
        print("[ERROR] Metadata file not found!")
        print("Please run preprocessing first to generate metadata.")
        return
    
    examples = [
        example_basic_usage,
        example_wildcard_selection,
        example_combined_selection,
        example_data_access,
        example_training_integration,
        example_feature_analysis,
        example_practical_scenarios,
    ]
    
    for example_func in examples:
        try:
            example_func()
        except Exception as e:
            print(f"\n[ERROR in {example_func.__name__}]: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "="*80)
    print("All examples completed!")
    print("="*80)

if __name__ == "__main__":
    run_all_examples()