"""Test metadata generation in preprocess.py"""

import sys
import os
sys.path.append('src')

from preprocess import load_analysis_results, IntelligentPreprocessor

def test_metadata_generation():
    print("Testing metadata generation...")
    
    # Load analysis results
    analysis_results = load_analysis_results()
    if not analysis_results:
        print("ERROR: Could not load analysis results")
        return False
    
    # Create preprocessor
    preprocessor = IntelligentPreprocessor(analysis_results)
    
    # Check metadata structure
    print(f"\nMetadata initialized:")
    print(f"  Total expected features: {preprocessor.feature_index}")
    print(f"  Feature groups: {len(preprocessor.feature_metadata['groups'])}")
    
    # Show group breakdown
    print("\nFeature groups:")
    for group, indices in sorted(preprocessor.feature_metadata['groups'].items()):
        group_name = group if group else "(no prefix)"
        print(f"  {group_name}: {len(indices)} features")
    
    # Check specific features
    print(f"\nFirst 5 features:")
    for i in range(min(5, len(preprocessor.feature_metadata['features']))):
        feat = preprocessor.feature_metadata['features'][i]
        print(f"  {feat['index']}: {feat['name']} ({feat['type']})")
    
    # Check array expansion
    print(f"\nArray features expanded:")
    array_features = [f for f in preprocessor.feature_metadata['features'] if f['type'] == 'embedding_element']
    print(f"  Total expanded features: {len(array_features)}")
    
    return True

if __name__ == "__main__":
    success = test_metadata_generation()
    if success:
        print("\n[OK] Metadata generation test passed!")
    else:
        print("\n[FAIL] Metadata generation test failed!")