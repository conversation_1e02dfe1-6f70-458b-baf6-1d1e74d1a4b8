#!/usr/bin/env python3
"""
诊断g5.24xlarge上多进程问题
"""

import multiprocessing as mp
import os
import sys
import platform
import subprocess
import time

def test_worker(i):
    """测试worker函数"""
    pid = os.getpid()
    print(f"Worker {i} started with PID {pid}")
    time.sleep(5)  # 模拟工作
    return f"Worker {i} (PID {pid}) completed"

def diagnose_multiprocessing():
    """诊断多进程问题"""
    
    print("="*60)
    print("多进程诊断")
    print("="*60)
    
    # 1. 系统信息
    print("\n1. 系统信息:")
    print(f"   Platform: {platform.system()}")
    print(f"   Python: {sys.version}")
    print(f"   CPU Count: {mp.cpu_count()}")
    print(f"   Current start method: {mp.get_start_method()}")
    print(f"   Available start methods: {mp.get_all_start_methods()}")
    
    # 2. 检查CUDA环境
    print("\n2. GPU/CUDA环境检查:")
    try:
        import torch
        print(f"   PyTorch available: Yes")
        print(f"   CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   CUDA devices: {torch.cuda.device_count()}")
            print(f"   CUDA version: {torch.version.cuda}")
    except ImportError:
        print("   PyTorch not available")
    
    # 检查CUDA环境变量
    cuda_visible = os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set')
    print(f"   CUDA_VISIBLE_DEVICES: {cuda_visible}")
    
    # 3. 测试不同的启动方法
    print("\n3. 测试多进程启动方法:")
    
    for method in mp.get_all_start_methods():
        print(f"\n   测试 '{method}' 方法:")
        try:
            mp.set_start_method(method, force=True)
            
            # 创建小进程池测试
            with mp.Pool(processes=4) as pool:
                start = time.time()
                results = pool.map(test_worker, range(4))
                duration = time.time() - start
                
                print(f"   ✓ '{method}' 成功！耗时: {duration:.2f}秒")
                for result in results:
                    print(f"      {result}")
                    
        except Exception as e:
            print(f"   ✗ '{method}' 失败: {str(e)}")
    
    # 4. 检查ulimit设置
    print("\n4. 系统限制检查:")
    try:
        result = subprocess.run(['ulimit', '-a'], shell=True, capture_output=True, text=True)
        print(result.stdout)
    except:
        print("   无法检查ulimit")
    
    # 5. 建议
    print("\n5. 诊断结果和建议:")
    
    if platform.system() == 'Linux':
        print("\n   在Linux + GPU环境下的常见问题:")
        print("   - fork方法可能与CUDA冲突")
        print("   - 建议使用spawn或forkserver方法")
        print("\n   修复方案:")
        print("   1. 修改config.py，强制使用spawn:")
        print("      'multiprocessing_start_method': 'spawn'  # 不要根据平台判断")
        print("\n   2. 或设置环境变量:")
        print("      export CUDA_VISIBLE_DEVICES=\"\"  # 在预处理时禁用GPU")
        print("\n   3. 在代码开始处添加:")
        print("      import multiprocessing")
        print("      multiprocessing.set_start_method('spawn', force=True)")

if __name__ == "__main__":
    diagnose_multiprocessing()