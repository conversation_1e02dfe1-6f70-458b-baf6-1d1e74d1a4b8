"""
Demo script to show feature selection functionality
"""

import os
import sys
import json
import numpy as np

# Add src directory to path
sys.path.insert(0, 'src')

from feature_selection import select_features_by_group, load_feature_metadata

def demo_feature_selection():
    """Demonstrate the feature selection functionality"""
    
    print("="*80)
    print("Feature Selection Demonstration")
    print("="*80)
    
    # Load feature metadata
    metadata_file = 'processed_data/feature_metadata_expanded.json'
    if not os.path.exists(metadata_file):
        print(f"Error: Feature metadata file not found at {metadata_file}")
        print("Please run generate_correct_feature_metadata.py first")
        return
    
    with open(metadata_file, 'r', encoding='utf-8') as f:
        feature_metadata = json.load(f)
    
    print(f"\nTotal features available: {feature_metadata['total_features']}")
    print("\nFeature groups:")
    for group, indices in sorted(feature_metadata['groups'].items()):
        group_name = group if group else "(no prefix)"
        features = [feature_metadata['features'][idx]['name'] for idx in indices]
        print(f"  {group_name}: {len(features)} features - {features}")
    
    # Demo different selection scenarios
    scenarios = [
        {
            'name': 'Exclude noise features',
            'include_groups': None,
            'exclude_groups': ['noise']
        },
        {
            'name': 'Include only user and item features',
            'include_groups': ['user', 'item'],
            'exclude_groups': None
        },
        {
            'name': 'Include only embedding features',
            'include_groups': ['user', 'item', 'context'],
            'exclude_groups': None
        },
        {
            'name': 'Include all except embeddings',
            'include_groups': None,
            'exclude_groups': ['user', 'item', 'context']
        },
        {
            'name': 'Include only non-prefixed features',
            'include_groups': [''],
            'exclude_groups': None
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{'='*60}")
        print(f"Scenario: {scenario['name']}")
        print("="*60)
        
        selected_indices = select_features_by_group(
            feature_metadata,
            include_groups=scenario['include_groups'],
            exclude_groups=scenario['exclude_groups']
        )
        
        # Show what would happen to the data
        print(f"\nEffect on data shape:")
        print(f"  Original: (samples, 17)")
        print(f"  After selection: (samples, {len(selected_indices)})")
        
        # Load a small sample to demonstrate
        train_features = np.load('processed_data/train_features.npy')
        sample_size = min(5, train_features.shape[0])
        
        print(f"\nExample data transformation (first {sample_size} samples):")
        print(f"  Original shape: {train_features[:sample_size].shape}")
        
        selected_features = train_features[:sample_size, selected_indices]
        print(f"  Selected shape: {selected_features.shape}")

if __name__ == "__main__":
    # Import the selection function from train_loss_optimized
    try:
        demo_feature_selection()
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()