# save_feature_metadata.py
"""
保存特征元数据，记录NPY文件中每个特征对应的原始列名
"""

import os
import json
import numpy as np
import pandas as pd
from config import PROCESSED_DATA_DIR, LABEL_COLUMN_NAME, EXCLUDE_COLUMNS
from preprocess import IntelligentPreprocessor, load_analysis_results
from s3_utils import list_parquet_files, read_parquet_file

def extract_feature_metadata():
    """
    从原始parquet文件中提取特征元数据
    记录每个特征位置对应的原始列名和组信息
    """
    
    # 加载分析结果
    analysis_results = load_analysis_results()
    if not analysis_results:
        # 如果没有分析结果，则从样本文件中提取
        print("Analysis results not found, extracting column info from sample file...")
        
        # 尝试读取一个样本文件
        sample_file = None
        for dataset in ['train', 'validation', 'test']:
            data_dir = os.path.join('local_test_data', 'small', dataset)
            if os.path.exists(data_dir):
                files = list_parquet_files(data_dir)
                if files:
                    sample_file = files[0]
                    break
        
        if not sample_file:
            raise ValueError("Cannot find sample parquet file to extract column info")
        
        # 读取样本文件
        df = read_parquet_file(sample_file)
        all_columns = list(df.columns)
        
        # 模拟分析结果结构
        analysis_results = {
            'summary': {
                'detected_label_column': LABEL_COLUMN_NAME,
                'array_columns': [],
                'numeric_columns': [],
                'categorical_columns': []
            }
        }
        
        # 简单分类列类型
        for col in all_columns:
            if col == LABEL_COLUMN_NAME or col in EXCLUDE_COLUMNS:
                continue
                
            sample_value = df[col].iloc[0]
            if isinstance(sample_value, (list, np.ndarray)):
                analysis_results['summary']['array_columns'].append(col)
            elif pd.api.types.is_numeric_dtype(df[col]):
                analysis_results['summary']['numeric_columns'].append(col)
            else:
                analysis_results['summary']['categorical_columns'].append(col)
    
    # 提取特征列顺序和组信息
    preprocessor = IntelligentPreprocessor(analysis_results)
    
    feature_metadata = {
        'features': [],
        'groups': {},
        'total_features': 0,
        'label_column': LABEL_COLUMN_NAME,
        'excluded_columns': EXCLUDE_COLUMNS
    }
    
    feature_index = 0
    
    # 处理数值列
    for col in preprocessor.numeric_columns:
        if col != preprocessor.label_column:
            # 提取前缀（如果有）
            prefix = extract_prefix(col)
            
            feature_info = {
                'index': feature_index,
                'name': col,
                'type': 'numeric',
                'group': prefix,
                'original_column': col
            }
            
            feature_metadata['features'].append(feature_info)
            
            # 记录到组信息
            if prefix not in feature_metadata['groups']:
                feature_metadata['groups'][prefix] = []
            feature_metadata['groups'][prefix].append(feature_index)
            
            feature_index += 1
    
    # 处理类别列
    for col in preprocessor.categorical_columns:
        if col != preprocessor.label_column:
            prefix = extract_prefix(col)
            
            feature_info = {
                'index': feature_index,
                'name': col,
                'type': 'categorical',
                'group': prefix,
                'original_column': col
            }
            
            feature_metadata['features'].append(feature_info)
            
            if prefix not in feature_metadata['groups']:
                feature_metadata['groups'][prefix] = []
            feature_metadata['groups'][prefix].append(feature_index)
            
            feature_index += 1
    
    # 处理数组列（展开的特征）
    for col in preprocessor.array_columns:
        array_dim = preprocessor.array_dimensions.get(col, 0)
        prefix = extract_prefix(col)
        
        if prefix not in feature_metadata['groups']:
            feature_metadata['groups'][prefix] = []
        
        for i in range(array_dim):
            feature_info = {
                'index': feature_index,
                'name': f"{col}_{i}",
                'type': 'array_element',
                'group': prefix,
                'original_column': col,
                'array_index': i
            }
            
            feature_metadata['features'].append(feature_info)
            feature_metadata['groups'][prefix].append(feature_index)
            
            feature_index += 1
    
    feature_metadata['total_features'] = feature_index
    
    # 保存元数据
    metadata_file = os.path.join(PROCESSED_DATA_DIR, 'feature_metadata_expanded.json')
    os.makedirs(PROCESSED_DATA_DIR, exist_ok=True)
    
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(feature_metadata, f, indent=2, ensure_ascii=False)
    
    print(f"Feature metadata saved to: {metadata_file}")
    print(f"Total features: {feature_metadata['total_features']}")
    print(f"Feature groups: {list(feature_metadata['groups'].keys())}")
    
    for group, indices in feature_metadata['groups'].items():
        print(f"  {group}: {len(indices)} features")
    
    return feature_metadata

def extract_prefix(column_name):
    """
    提取列名的前缀（组名）
    例如: "user_embedding" -> "user"
          "click_position" -> "click"
          "age" -> ""（无前缀）
    """
    if '_' in column_name:
        return column_name.split('_')[0]
    return ''  # 无前缀的列归为空组

if __name__ == "__main__":
    extract_feature_metadata()