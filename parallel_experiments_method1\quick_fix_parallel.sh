#!/bin/bash
# 快速修复g5.24xlarge并行处理问题的脚本

echo "==================================="
echo "快速修复g5.24xlarge并行处理"
echo "==================================="

# 1. 设置环境变量
echo "设置环境变量..."
export IS_EC2_DEPLOYMENT=true
export AWS_MAX_POOL_CONNECTIONS=100
export FORCE_NUM_WORKERS=48
export FORCE_MEMORY_LIMIT_GB=350

echo "环境变量设置完成："
echo "  IS_EC2_DEPLOYMENT=$IS_EC2_DEPLOYMENT"
echo "  AWS_MAX_POOL_CONNECTIONS=$AWS_MAX_POOL_CONNECTIONS"
echo "  FORCE_NUM_WORKERS=$FORCE_NUM_WORKERS"
echo "  FORCE_MEMORY_LIMIT_GB=$FORCE_MEMORY_LIMIT_GB"

# 2. 显示当前系统信息
echo ""
echo "系统信息："
echo "  CPU核心数: $(nproc)"
echo "  内存: $(free -h | grep Mem | awk '{print $2}')"
echo "  当前Python进程数: $(ps aux | grep python | grep -v grep | wc -l)"

# 3. 运行并行处理
echo ""
echo "启动并行处理（48个worker）..."
echo "命令: python src/run_parallel_processing.py --workers 48"
echo ""

# 启动处理
python src/run_parallel_processing.py --workers 48