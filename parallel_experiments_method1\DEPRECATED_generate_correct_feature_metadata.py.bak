"""
Generate correct feature metadata based on actual NPY file structure
"""

import os
import json
import pandas as pd
import numpy as np

def generate_correct_feature_metadata():
    """
    Generate correct feature metadata by analyzing what's actually in the NPY files
    This accounts for the fact that embedding columns are NOT expanded in NPY files
    """
    
    # Read a sample parquet file to understand the original columns
    sample_file = r"local_test_data\small\train\data_chunk_000.parquet"
    df = pd.read_parquet(sample_file)
    
    print(f"Original columns: {list(df.columns)}")
    
    # Load actual NPY file to confirm feature count
    train_features = np.load('processed_data/train_features.npy')
    print(f"NPY file shape: {train_features.shape}")
    
    # Define excluded columns and label column based on config
    label_column = "click"
    excluded_columns = ["user_id", "item_id", "timestamp"]
    
    feature_metadata = {
        'features': [],
        'groups': {},
        'total_features': 0,
        'label_column': label_column,
        'excluded_columns': excluded_columns,
        'original_columns': list(df.columns)
    }
    
    feature_index = 0
    
    # Process columns in the order they appear (excluding label and excluded columns)
    for col in df.columns:
        # Skip label and excluded columns
        if col == label_column or col in excluded_columns:
            continue
        
        # Get column prefix/group
        prefix = col.split('_')[0] if '_' in col else ''
        
        # Add to groups
        if prefix not in feature_metadata['groups']:
            feature_metadata['groups'][prefix] = []
        
        # Determine type
        sample_value = df[col].iloc[0]
        if isinstance(sample_value, (list, np.ndarray)):
            col_type = 'embedding'  # Embeddings are kept as single columns in NPY
        elif pd.api.types.is_numeric_dtype(df[col]):
            col_type = 'numeric'
        else:
            col_type = 'categorical'
        
        feature_info = {
            'index': feature_index,
            'name': col,
            'type': col_type,
            'group': prefix,
            'original_column': col
        }
        
        if col_type == 'embedding':
            feature_info['embedding_size'] = len(sample_value)
        
        feature_metadata['features'].append(feature_info)
        feature_metadata['groups'][prefix].append(feature_index)
        feature_index += 1
    
    feature_metadata['total_features'] = feature_index
    
    # Save metadata
    os.makedirs('processed_data', exist_ok=True)
    metadata_file = 'processed_data/feature_metadata.json'
    
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(feature_metadata, f, indent=2, ensure_ascii=False)
    
    print(f"\nCorrect feature metadata saved to: {metadata_file}")
    print(f"Total features: {feature_metadata['total_features']}")
    print(f"\nFeature groups:")
    
    for group, indices in sorted(feature_metadata['groups'].items()):
        group_name = group if group else "(no prefix)"
        print(f"  {group_name}: {len(indices)} features")
    
    # Display all features with their details
    print(f"\nAll {feature_metadata['total_features']} features:")
    for feat in feature_metadata['features']:
        extra_info = ""
        if feat['type'] == 'embedding':
            extra_info = f", size={feat['embedding_size']}"
        print(f"  {feat['index']}: {feat['name']} (type: {feat['type']}, group: {feat['group'] or 'none'}{extra_info})")
    
    return feature_metadata

if __name__ == "__main__":
    generate_correct_feature_metadata()