"""Test preprocessing without parallel processing"""

import sys
import os
import json
sys.path.append('src')

from preprocess import load_analysis_results, IntelligentPreprocessor, process_single_dataset

def test_simple_preprocess():
    print("Testing simple preprocessing with metadata generation...")
    
    # Load analysis results
    analysis_results = load_analysis_results()
    if not analysis_results:
        print("ERROR: Could not load analysis results")
        return False
    
    # Create preprocessor
    preprocessor = IntelligentPreprocessor(analysis_results)
    print(f"Preprocessor initialized with {preprocessor.feature_index} expected features")
    
    # Process just the train dataset
    data_dir = 'local_test_data/small/train'
    dataset_name = 'train'
    
    # Remove existing metadata
    metadata_file = 'processed_data/feature_metadata_expanded.json'
    if os.path.exists(metadata_file):
        os.rename(metadata_file, metadata_file + '.bak3')
    
    try:
        # Process dataset
        print(f"\nProcessing {dataset_name} dataset...")
        success = process_single_dataset(preprocessor, data_dir, dataset_name)
        
        if success:
            print(f"[OK] Dataset processed successfully")
            
            # Check if metadata was generated
            if os.path.exists(metadata_file):
                print(f"[OK] Metadata generated: {metadata_file}")
                
                # Load and display metadata
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                print(f"\nMetadata summary:")
                print(f"  Total features: {metadata['total_features']}")
                print(f"  NPY shape: {metadata.get('npy_shape', 'Not set')}")
                print(f"  Feature groups: {len(metadata['groups'])}")
                
                # Count feature types
                feature_types = {}
                for feat in metadata['features']:
                    feat_type = feat['type']
                    feature_types[feat_type] = feature_types.get(feat_type, 0) + 1
                
                print(f"\nFeature types:")
                for feat_type, count in sorted(feature_types.items()):
                    print(f"    {feat_type}: {count}")
                    
                return True
            else:
                print(f"[FAIL] Metadata not generated")
                return False
        else:
            print(f"[FAIL] Dataset processing failed")
            return False
            
    finally:
        # Restore backup
        if os.path.exists(metadata_file + '.bak3'):
            if os.path.exists(metadata_file):
                os.remove(metadata_file)
            os.rename(metadata_file + '.bak3', metadata_file)
            print("\nRestored backup metadata")

if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.WARNING)
    
    success = test_simple_preprocess()
    if success:
        print("\n[OK] Simple preprocessing test passed!")
    else:
        print("\n[FAIL] Simple preprocessing test failed!")