#!/usr/bin/env python3
"""
紧急修复g5实例上的多进程问题
"""

import os
import shutil
from datetime import datetime

def emergency_fix():
    """紧急修复：强制使用spawn方法"""
    
    print("紧急修复：强制所有平台使用spawn方法")
    
    # 1. 修改config.py
    config_file = "src/config.py"
    backup_file = f"src/config.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print(f"备份 {config_file} -> {backup_file}")
    shutil.copy2(config_file, backup_file)
    
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找并替换
    old_line = "'multiprocessing_start_method': 'spawn' if IS_WINDOWS else 'fork',"
    new_line = "'multiprocessing_start_method': 'spawn',  # 强制所有平台使用spawn，避免GPU冲突"
    
    if old_line in content:
        content = content.replace(old_line, new_line)
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✓ 已修改config.py，强制使用spawn方法")
    else:
        print("! 未找到需要修改的行，可能已经修改过")
    
    # 2. 创建运行脚本
    run_script = """#!/bin/bash
# 修复后的运行脚本

echo "使用修复后的配置运行..."

# 禁用GPU（仅在预处理时）
export CUDA_VISIBLE_DEVICES=""

# 确保使用spawn
export PYTHONHASHSEED=0

# 运行
python src/run_parallel_processing.py --workers 32

echo "如果还有问题，尝试："
echo "python diagnose_multiprocessing.py"
"""
    
    with open("run_fixed.sh", 'w') as f:
        f.write(run_script)
    
    os.chmod("run_fixed.sh", 0o755)
    print("\n✓ 创建了run_fixed.sh")
    print("\n立即运行：")
    print("./run_fixed.sh")
    
    print("\n如果还不行，运行诊断：")
    print("python diagnose_multiprocessing.py")

if __name__ == "__main__":
    emergency_fix()