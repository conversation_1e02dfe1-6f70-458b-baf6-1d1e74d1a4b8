#!/usr/bin/env python3
"""
进程状态跟踪器 - 可以设置logger level隐藏其他日志，只显示TRACK级别
"""

import logging
import sys
import time
from collections import defaultdict

# 创建新的日志级别 - 专门用于进程状态跟踪
PROCESS_TRACK = 45  # 高于ERROR(40)，这样ERROR也会被隐藏

def process_track(self, message, *args, **kwargs):
    """进程跟踪日志方法"""
    if self.isEnabledFor(PROCESS_TRACK):
        self._log(PROCESS_TRACK, message, args, **kwargs)

# 添加自定义日志级别
logging.addLevelName(PROCESS_TRACK, "TRACK")
logging.Logger.process_track = process_track

def set_track_only_mode():
    """设置为只显示TRACK级别日志，隐藏INFO/DEBUG等"""
    # 设置根日志器级别为PROCESS_TRACK，这样INFO/DEBUG都不会显示
    root_logger = logging.getLogger()
    root_logger.setLevel(PROCESS_TRACK)

    # 强制设置所有现有logger的级别为PROCESS_TRACK
    for name in logging.Logger.manager.loggerDict:
        logger = logging.getLogger(name)
        logger.setLevel(PROCESS_TRACK)

    # 特别设置可能输出路径信息的logger
    logging.getLogger('s3_utils').setLevel(PROCESS_TRACK)
    logging.getLogger('data_analyzer').setLevel(PROCESS_TRACK)
    logging.getLogger('parallel_processor').setLevel(PROCESS_TRACK)
    logging.getLogger('preprocess').setLevel(PROCESS_TRACK)

    # 禁用第三方库的详细日志
    logging.getLogger('botocore').setLevel(logging.CRITICAL)
    logging.getLogger('boto3').setLevel(logging.CRITICAL)
    logging.getLogger('s3fs').setLevel(logging.CRITICAL)
    logging.getLogger('urllib3').setLevel(logging.CRITICAL)
    logging.getLogger('fsspec').setLevel(logging.CRITICAL)
    logging.getLogger('pandas').setLevel(logging.CRITICAL)
    logging.getLogger('pyarrow').setLevel(logging.CRITICAL)

    # 设置所有worker logger
    for i in range(100):  # 覆盖可能的worker进程
        logging.getLogger(f'worker_{i}').setLevel(PROCESS_TRACK)

    print("[OK] Logger level set to TRACK mode - showing only process tracking info")

def set_normal_mode():
    """恢复正常日志模式"""
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    print("[OK] Logger level restored to normal mode - showing all logs")

class ProcessTracker:
    """进程状态跟踪器"""

    def __init__(self):
        self.logger = logging.getLogger("ProcessTracker")
        self.logger.setLevel(PROCESS_TRACK)

        # 如果没有handler，添加一个
        if not self.logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            handler.setLevel(PROCESS_TRACK)
            formatter = logging.Formatter('%(asctime)s - TRACK - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

        # 进程统计
        self.workers = {}  # worker_id -> {status, start_time, operation}
        self.requests = defaultdict(int)  # operation -> count
        self.responses = defaultdict(int)  # operation -> count
        self.timeouts = defaultdict(int)  # operation -> count

    def log_request(self, operation):
        """记录请求发送"""
        self.requests[operation] += 1
        self.logger.process_track(f"REQ: {operation} (total: {self.requests[operation]})")

    def log_response(self, operation, status="SUCCESS"):
        """记录响应接收"""
        self.responses[operation] += 1
        self.logger.process_track(f"RESP: {operation}_{status} (total: {self.responses[operation]})")

    def log_worker_start(self, worker_id, operation):
        """记录worker开始"""
        self.workers[worker_id] = {
            'status': 'RUNNING',
            'start_time': time.time(),
            'operation': operation
        }
        self.logger.process_track(f"WORKER_{worker_id}: START {operation}")

    def log_worker_complete(self, worker_id, result="SUCCESS"):
        """记录worker完成"""
        if worker_id in self.workers:
            duration = time.time() - self.workers[worker_id]['start_time']
            self.workers[worker_id]['status'] = 'COMPLETE'
            self.logger.process_track(f"WORKER_{worker_id}: {result} ({duration:.1f}s)")

    def log_worker_timeout(self, worker_id):
        """记录worker超时"""
        if worker_id in self.workers:
            operation = self.workers[worker_id]['operation']
            self.timeouts[operation] += 1
            self.workers[worker_id]['status'] = 'TIMEOUT'
            self.logger.process_track(f"WORKER_{worker_id}: TIMEOUT")

    def log_stats(self):
        """记录统计信息"""
        running = sum(1 for w in self.workers.values() if w['status'] == 'RUNNING')
        complete = sum(1 for w in self.workers.values() if w['status'] == 'COMPLETE')
        timeout = sum(1 for w in self.workers.values() if w['status'] == 'TIMEOUT')

        self.logger.process_track(f"STATS: workers={len(self.workers)} running={running} complete={complete} timeout={timeout}")

        for op in self.requests:
            req_count = self.requests[op]
            resp_count = self.responses[op]
            timeout_count = self.timeouts[op]
            self.logger.process_track(f"STATS_{op}: req={req_count} resp={resp_count} timeout={timeout_count}")

# 全局跟踪器实例
_tracker = ProcessTracker()

# 简化的接口函数
def track_request(operation):
    """跟踪请求"""
    _tracker.log_request(operation)

def track_response(operation, status="SUCCESS"):
    """跟踪响应"""
    _tracker.log_response(operation, status)

def track_worker_start(worker_id, operation):
    """跟踪worker开始"""
    _tracker.log_worker_start(worker_id, operation)

def track_worker_complete(worker_id, result="SUCCESS"):
    """跟踪worker完成"""
    _tracker.log_worker_complete(worker_id, result)

def track_worker_timeout(worker_id):
    """跟踪worker超时"""
    _tracker.log_worker_timeout(worker_id)

def track_stats():
    """输出统计信息"""
    _tracker.log_stats()

def track_s3_request(operation, details=""):
    """跟踪S3请求（不包含路径）"""
    _tracker.logger.process_track(f"S3_REQ: {operation} {details}")

def track_s3_response(operation, status, details=""):
    """跟踪S3响应（不包含路径）"""
    _tracker.logger.process_track(f"S3_RESP: {operation}_{status} {details}")

def track_thread_create(thread_id, operation):
    """跟踪线程创建"""
    _tracker.logger.process_track(f"THREAD_CREATE: {thread_id} {operation}")

def track_thread_complete(thread_id, result):
    """跟踪线程完成"""
    _tracker.logger.process_track(f"THREAD_COMPLETE: {thread_id} {result}")

def track_thread_timeout(thread_id, operation):
    """跟踪线程超时"""
    _tracker.logger.process_track(f"THREAD_TIMEOUT: {thread_id} {operation}")

def track_pool_status(active_threads, total_threads, operation=""):
    """跟踪线程池状态"""
    _tracker.logger.process_track(f"POOL_STATUS: active={active_threads} total={total_threads} {operation}")

def track_memory_usage(usage_mb, operation=""):
    """跟踪内存使用"""
    _tracker.logger.process_track(f"MEMORY: {usage_mb}MB {operation}")

def track_file_count(count, operation=""):
    """跟踪文件数量"""
    _tracker.logger.process_track(f"FILES: count={count} {operation}")

def track_data_size(rows, cols, operation=""):
    """跟踪数据大小"""
    _tracker.logger.process_track(f"DATA_SIZE: rows={rows} cols={cols} {operation}")
