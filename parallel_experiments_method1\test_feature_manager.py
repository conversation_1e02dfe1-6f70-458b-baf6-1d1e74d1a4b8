"""
Test script for FeatureManager functionality
"""

import sys
import os
import numpy as np

# Set UTF-8 encoding for Windows
if sys.platform == 'win32':
    os.environ['PYTHONIOENCODING'] = 'utf-8'

sys.path.append('src')

from feature_manager import FeatureManager, FeatureAccessor, create_feature_manager


def test_feature_manager():
    """Test FeatureManager functionality"""
    
    print("="*80)
    print("Testing FeatureManager")
    print("="*80)
    
    # Create FeatureManager
    try:
        fm = create_feature_manager()
        print(f"[OK] Successfully created FeatureManager")
        print(f"  Total features: {fm.total_features}")
        print(f"  Feature groups: {fm.feature_groups}")
    except Exception as e:
        print(f"[FAIL] Failed to create FeatureManager: {e}")
        return
    
    # Test feature selection scenarios
    test_cases = [
        {
            'name': 'Exclude noise features',
            'kwargs': {'exclude_groups': ['noise']},
            'expected_count': 172  # 174 - 2
        },
        {
            'name': 'Include only embeddings',
            'kwargs': {'by_groups': ['user', 'item', 'context']},
            'expected_count': 160  # 64 + 64 + 32
        },
        {
            'name': 'Include only user features',
            'kwargs': {'by_groups': ['user']},
            'expected_count': 64
        },
        {
            'name': 'Exclude all embeddings',
            'kwargs': {'exclude_groups': ['user', 'item', 'context']},
            'expected_count': 14  # 174 - 160
        },
        {
            'name': 'Select by feature names with wildcard',
            'kwargs': {'by_names': ['user_embedding_*']},
            'expected_count': 64
        },
        {
            'name': 'Select specific features',
            'kwargs': {'by_names': ['age', 'gender', 'price']},
            'expected_count': 3
        }
    ]
    
    print("\n" + "="*60)
    print("Testing feature selection")
    print("="*60)
    
    for test in test_cases:
        print(f"\nTest: {test['name']}")
        print(f"Parameters: {test['kwargs']}")
        
        try:
            indices = fm.get_feature_indices(**test['kwargs'])
            print(f"[OK] Selected {len(indices)} features")
            
            if len(indices) == test['expected_count']:
                print(f"[OK] Matches expected count: {test['expected_count']}")
            else:
                print(f"[FAIL] Expected {test['expected_count']} features, got {len(indices)}")
            
            # Show first few feature names
            if len(indices) <= 10:
                names = fm.get_feature_names(indices)
                print(f"  Features: {names}")
            else:
                names = fm.get_feature_names(indices[:5])
                print(f"  First 5 features: {names}")
                
        except Exception as e:
            print(f"[FAIL] Error: {e}")
    
    # Test FeatureAccessor
    print("\n" + "="*60)
    print("Testing FeatureAccessor")
    print("="*60)
    
    # Load sample data
    try:
        train_features = np.load('processed_data/train_features.npy')
        print(f"[OK] Loaded training data: shape={train_features.shape}")
        
        # Create accessor
        accessor = FeatureAccessor(train_features[:100], fm)  # Use first 100 samples
        
        # Test different access methods
        print("\nTesting access methods:")
        
        # Access by group
        user_features = accessor['user']
        print(f"[OK] Group access: user features shape = {user_features.shape}")
        
        # Access by original column
        user_emb = accessor.get_original_column('user_embedding')
        print(f"[OK] Original column access: user_embedding shape = {user_emb.shape}")
        
        # Access by feature name
        if 'age' in fm.name_to_index:
            age_feature = accessor['age']
            print(f"[OK] Feature name access: age shape = {age_feature.shape}")
        
        # Test slicing with FeatureManager
        clean_data = fm.get_feature_slice(train_features[:100], exclude_groups=['noise'])
        print(f"[OK] Feature slice: excluding noise gives shape = {clean_data.shape}")
        
    except Exception as e:
        print(f"[FAIL] Error testing FeatureAccessor: {e}")
    
    # Display feature manager info
    print("\n" + "="*60)
    print("Feature Manager Summary")
    print("="*60)
    fm.describe()
    
    print("\n[OK] All tests completed!")


if __name__ == "__main__":
    test_feature_manager()