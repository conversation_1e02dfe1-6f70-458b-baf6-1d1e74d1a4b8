"""
Test feature selection with expanded arrays
"""
import numpy as np
import json
from src.feature_selection import select_features_by_group

# Load expanded metadata
with open('processed_data/feature_metadata_expanded.json', 'r') as f:
    metadata = json.load(f)

print(f"Total features: {metadata['total_features']}")
print(f"\nFeature groups:")
for group, indices in sorted(metadata['groups'].items()):
    group_name = group if group else "(no prefix)"
    print(f"  {group_name}: {len(indices)} features")

# Test scenarios
test_cases = [
    ("Exclude noise features", None, ["noise"]),
    ("Include only embeddings", ["user", "item", "context"], None),
    ("Include only non-embeddings", None, ["user", "item", "context"]),
    ("Include only user features", ["user"], None),
]

# Load actual NPY data
train_features = np.load('processed_data/train_features.npy')
print(f"\nActual NPY shape: {train_features.shape}")

for name, include, exclude in test_cases:
    print(f"\n{'='*60}")
    print(f"Test: {name}")
    print(f"Include groups: {include}")
    print(f"Exclude groups: {exclude}")
    
    selected_indices = select_features_by_group(metadata, include, exclude)
    
    if selected_indices is not None:
        print(f"Selected features: {len(selected_indices)}")
        
        # Apply selection
        selected_data = train_features[:5, selected_indices]
        print(f"Data shape after selection: {selected_data.shape}")
        
        # Show which groups are included
        selected_groups = {}
        for idx in selected_indices:
            for feat in metadata['features']:
                if feat['index'] == idx:
                    group = feat['group'] if feat['group'] else "(no prefix)"
                    if group not in selected_groups:
                        selected_groups[group] = 0
                    selected_groups[group] += 1
                    break
        
        print("Selected groups:")
        for group, count in sorted(selected_groups.items()):
            print(f"  {group}: {count} features")